#!/usr/bin/env python3
"""
Steam Tools Downloader v2 - Build Script
Packages the GUI application into a single executable file using PyInstaller
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    RESET = '\033[0m'
    BOLD = '\033[1m'

def print_colored(message, color=Colors.WHITE):
    """Print colored message"""
    print(f"{color}{message}{Colors.RESET}")

def print_header(message):
    """Print header with decoration"""
    print_colored("=" * 60, Colors.CYAN)
    print_colored(f" {message}", Colors.CYAN + Colors.BOLD)
    print_colored("=" * 60, Colors.CYAN)

def print_step(step_num, message):
    """Print step with numbering"""
    print_colored(f"\n[Step {step_num}] {message}", Colors.YELLOW + Colors.BOLD)

def print_success(message):
    """Print success message"""
    print_colored(f"✓ {message}", Colors.GREEN)

def print_error(message):
    """Print error message"""
    print_colored(f"✗ {message}", Colors.RED)

def print_warning(message):
    """Print warning message"""
    print_colored(f"⚠ {message}", Colors.YELLOW)

def run_command(command, description=""):
    """Run command and handle errors"""
    if description:
        print_colored(f"  Running: {description}", Colors.BLUE)
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        print_error(f"Command failed: {command}")
        print_error(f"Error: {e.stderr}")
        return False, e.stderr

def check_dependencies():
    """Check if required tools are installed"""
    print_step(1, "Checking Dependencies")
    
    # Check Python
    try:
        python_version = subprocess.check_output([sys.executable, "--version"], 
                                               text=True).strip()
        print_success(f"Python found: {python_version}")
    except:
        print_error("Python not found!")
        return False
    
    # Check PyInstaller
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "show", "pyinstaller"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print_success("PyInstaller is installed")
        else:
            print_warning("PyInstaller not found, will install it")
            success, _ = run_command(f"{sys.executable} -m pip install pyinstaller", 
                                   "Installing PyInstaller")
            if not success:
                print_error("Failed to install PyInstaller")
                return False
            print_success("PyInstaller installed successfully")
    except:
        print_error("Failed to check PyInstaller")
        return False
    
    return True

def install_dependencies():
    """Install required dependencies"""
    print_step(2, "Installing Dependencies")
    
    # Install from requirements.txt
    requirements_path = Path("../requirements.txt")
    if requirements_path.exists():
        success, _ = run_command(f"{sys.executable} -m pip install -r {requirements_path}", 
                               "Installing from requirements.txt")
        if success:
            print_success("Dependencies installed from requirements.txt")
        else:
            print_error("Failed to install some dependencies")
            return False
    else:
        print_warning("requirements.txt not found, installing individual packages")
        
        packages = [
            "requests>=2.25.1",
            "pycryptodome>=3.15.0",
            "pywin32>=227"
        ]
        
        for package in packages:
            success, _ = run_command(f"{sys.executable} -m pip install {package}", 
                                   f"Installing {package}")
            if success:
                print_success(f"Installed {package}")
            else:
                print_warning(f"Failed to install {package}")
    
    return True

def prepare_build_environment():
    """Prepare the build environment"""
    print_step(3, "Preparing Build Environment")
    
    # Create build directories
    build_dir = Path("build")
    dist_dir = Path("dist")
    
    if build_dir.exists():
        shutil.rmtree(build_dir)
        print_success("Cleaned build directory")
    
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
        print_success("Cleaned dist directory")
    
    # Copy keyauth.py to current directory for easier packaging
    keyauth_src = Path("../../src/keyauth.py")
    keyauth_dst = Path("keyauth.py")
    
    if keyauth_src.exists():
        shutil.copy2(keyauth_src, keyauth_dst)
        print_success("Copied keyauth.py to build directory")
    else:
        print_error("keyauth.py not found in src directory")
        return False
    
    return True

def build_executable():
    """Build the executable using PyInstaller"""
    print_step(4, "Building Executable")
    
    # Build command
    build_cmd = f"{sys.executable} -m PyInstaller steam_tools_gui.spec --clean --noconfirm"
    
    print_colored("  This may take a few minutes...", Colors.BLUE)
    success, output = run_command(build_cmd, "Building with PyInstaller")
    
    if success:
        print_success("Executable built successfully")
        return True
    else:
        print_error("Failed to build executable")
        print_error(output)
        return False

def create_distribution():
    """Create distribution package"""
    print_step(5, "Creating Distribution Package")
    
    # Create distribution directory
    dist_name = "SteamToolsDownloader_v2_Distribution"
    dist_path = Path(dist_name)
    
    if dist_path.exists():
        shutil.rmtree(dist_path)
    
    dist_path.mkdir()
    
    # Copy executable
    exe_src = Path("dist/SteamToolsDownloader_v2.exe")
    if exe_src.exists():
        shutil.copy2(exe_src, dist_path / "SteamToolsDownloader_v2.exe")
        print_success("Copied executable to distribution")
    else:
        print_error("Executable not found!")
        return False
    
    # Copy documentation
    readme_src = Path("../README.md")
    if readme_src.exists():
        shutil.copy2(readme_src, dist_path / "README.md")
        print_success("Copied README.md")
    
    # Create launcher batch file
    launcher_content = '''@echo off
title Steam Tools Downloader v2
echo Starting Steam Tools Downloader v2...
echo.
SteamToolsDownloader_v2.exe
echo.
echo Application closed.
pause'''
    
    with open(dist_path / "Launch_SteamTools.bat", "w") as f:
        f.write(launcher_content)
    print_success("Created launcher batch file")
    
    # Create distribution info
    info_content = f'''Steam Tools Downloader v2 - Distribution Package
Generated on: {time.strftime("%Y-%m-%d %H:%M:%S")}

Files included:
- SteamToolsDownloader_v2.exe (Main application)
- Launch_SteamTools.bat (Launcher script)
- README.md (Documentation)

Instructions:
1. Run SteamToolsDownloader_v2.exe directly, or
2. Use Launch_SteamTools.bat for a more user-friendly experience

Requirements:
- Windows 7 or later
- Internet connection for license validation and downloads
- Valid Keyauth license key

For support or issues, please refer to the README.md file.
'''
    
    with open(dist_path / "DISTRIBUTION_INFO.txt", "w") as f:
        f.write(info_content)
    print_success("Created distribution info file")
    
    return True

def cleanup():
    """Clean up temporary files"""
    print_step(6, "Cleaning Up")
    
    # Remove copied keyauth.py
    keyauth_temp = Path("keyauth.py")
    if keyauth_temp.exists():
        keyauth_temp.unlink()
        print_success("Removed temporary keyauth.py")
    
    # Optionally remove build directory (keep dist for debugging)
    build_dir = Path("build")
    if build_dir.exists():
        shutil.rmtree(build_dir)
        print_success("Removed build directory")

def main():
    """Main build process"""
    print_header("Steam Tools Downloader v2 - Build Script")
    print_colored("This script will package the GUI application into a single executable", Colors.WHITE)
    print()
    
    start_time = time.time()
    
    try:
        # Step 1: Check dependencies
        if not check_dependencies():
            print_error("Dependency check failed!")
            return False
        
        # Step 2: Install dependencies
        if not install_dependencies():
            print_error("Dependency installation failed!")
            return False
        
        # Step 3: Prepare build environment
        if not prepare_build_environment():
            print_error("Build environment preparation failed!")
            return False
        
        # Step 4: Build executable
        if not build_executable():
            print_error("Executable build failed!")
            return False
        
        # Step 5: Create distribution
        if not create_distribution():
            print_error("Distribution creation failed!")
            return False
        
        # Step 6: Cleanup
        cleanup()
        
        # Success message
        elapsed_time = time.time() - start_time
        print_header("Build Completed Successfully!")
        print_success(f"Build completed in {elapsed_time:.1f} seconds")
        print_colored("\nDistribution package created:", Colors.GREEN + Colors.BOLD)
        print_colored("  📁 SteamToolsDownloader_v2_Distribution/", Colors.GREEN)
        print_colored("     ├── SteamToolsDownloader_v2.exe", Colors.GREEN)
        print_colored("     ├── Launch_SteamTools.bat", Colors.GREEN)
        print_colored("     ├── README.md", Colors.GREEN)
        print_colored("     └── DISTRIBUTION_INFO.txt", Colors.GREEN)
        print()
        print_colored("🚀 Ready to deploy! You can now distribute the entire folder.", Colors.CYAN + Colors.BOLD)
        
        return True
        
    except KeyboardInterrupt:
        print_error("\nBuild interrupted by user")
        return False
    except Exception as e:
        print_error(f"Unexpected error: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
