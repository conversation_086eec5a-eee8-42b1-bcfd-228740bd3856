<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steam Tools - License Activation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <style>
        /* Import Steam Theme */
        :root {
            --background: #0e1419;
            --foreground: #c7d5e0;
            --card: #1b2838;
            --card-foreground: #c7d5e0;
            --popover: #1b2838;
            --popover-foreground: #c7d5e0;
            --primary: #66c0f4;
            --primary-foreground: #0e1419;
            --secondary: #2a475e;
            --secondary-foreground: #c7d5e0;
            --muted: #171a21;
            --muted-foreground: #8b98a5;
            --accent: #4c6b22;
            --accent-foreground: #c7d5e0;
            --destructive: #cd412b;
            --destructive-foreground: #ffffff;
            --border: #2a475e;
            --input: #1b2838;
            --ring: #66c0f4;
            --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
            --radius: 0.375rem;
            --steam-gradient-primary: linear-gradient(135deg, #66c0f4 0%, #4c9eff 100%);
            --steam-gradient-secondary: linear-gradient(135deg, #2a475e 0%, #1b2838 100%);
            --steam-gradient-success: linear-gradient(135deg, #4c6b22 0%, #5c7e10 100%);
            --steam-gradient-card: linear-gradient(145deg, #1b2838 0%, #16202d 100%);
            --steam-glow: 0 0 20px rgba(102, 192, 244, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-sans) !important;
            background: var(--background) !important;
            color: var(--foreground) !important;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Steam-inspired animations */
        @keyframes steamGlow {
            0%, 100% { box-shadow: var(--steam-glow); }
            50% { box-shadow: 0 0 30px rgba(102, 192, 244, 0.5); }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes progressFill {
            from { width: 0%; }
            to { width: var(--progress-width, 0%); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Main container */
        .steam-container {
            background: var(--background);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
        }

        .steam-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 50%, rgba(102, 192, 244, 0.05) 0%, transparent 70%);
            pointer-events: none;
        }

        /* Header */
        .steam-header {
            text-align: center;
            margin-bottom: 3rem;
            animation: slideInUp 0.8s ease-out;
        }

        .steam-title {
            font-size: 2.5rem !important;
            font-weight: 700 !important;
            color: var(--primary) !important;
            margin-bottom: 0.5rem;
            text-shadow: 0 0 20px rgba(102, 192, 244, 0.3);
        }

        .steam-subtitle {
            font-size: 1.125rem !important;
            color: var(--muted-foreground) !important;
            font-weight: 400 !important;
        }

        /* Main card */
        .steam-card {
            background: var(--steam-gradient-card);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 3rem;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 20px 25px 0 rgba(0, 0, 0, 0.5), 0 10px 10px 0 rgba(0, 0, 0, 0.4);
            animation: slideInUp 0.8s ease-out 0.2s both;
            position: relative;
        }

        .steam-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
            opacity: 0.5;
        }

        /* Logo area */
        .steam-logo {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: var(--steam-gradient-secondary);
            border-radius: var(--radius);
            border: 1px solid var(--border);
        }

        .steam-logo-icon {
            width: 64px;
            height: 64px;
            color: var(--primary);
            margin: 0 auto 1rem;
            filter: drop-shadow(var(--steam-glow));
        }

        .steam-logo-text {
            font-size: 1.5rem !important;
            font-weight: 600 !important;
            color: var(--primary) !important;
        }

        /* License input */
        .license-section {
            margin-bottom: 2rem;
        }

        .license-label {
            display: block;
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            color: var(--foreground) !important;
            margin-bottom: 0.5rem;
        }

        .license-input {
            width: 100%;
            padding: 1rem;
            background: var(--input) !important;
            border: 2px solid var(--border) !important;
            border-radius: var(--radius) !important;
            color: var(--foreground) !important;
            font-family: var(--font-mono) !important;
            font-size: 1.125rem !important;
            text-align: center;
            letter-spacing: 0.1em;
            transition: all 0.3s ease;
        }

        .license-input:focus {
            outline: none !important;
            border-color: var(--primary) !important;
            box-shadow: var(--steam-glow), 0 0 0 3px rgba(102, 192, 244, 0.1) !important;
            transform: scale(1.02);
        }

        .license-input.error {
            border-color: var(--destructive) !important;
            animation: shake 0.4s ease-in-out;
        }

        .license-input.success {
            border-color: var(--accent) !important;
            box-shadow: 0 0 20px rgba(76, 107, 34, 0.3);
        }

        /* Activate button */
        .activate-btn {
            width: 100%;
            padding: 1rem 2rem;
            background: var(--steam-gradient-primary) !important;
            border: none !important;
            border-radius: var(--radius) !important;
            color: var(--primary-foreground) !important;
            font-size: 1.125rem !important;
            font-weight: 600 !important;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .activate-btn:hover {
            transform: scale(1.05);
            box-shadow: var(--steam-glow);
        }

        .activate-btn:active {
            transform: scale(0.98);
        }

        .activate-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .activate-btn.loading {
            color: transparent;
        }

        .activate-btn .spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid var(--primary-foreground);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            opacity: 0;
        }

        .activate-btn.loading .spinner {
            opacity: 1;
        }

        /* Progress bar */
        .progress-section {
            margin-bottom: 2rem;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.875rem !important;
            color: var(--foreground) !important;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--muted);
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: var(--steam-gradient-primary);
            border-radius: 4px;
            transition: width 0.8s ease-out;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* HWID section */
        .hwid-section {
            background: var(--steam-gradient-secondary);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .hwid-label {
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            color: var(--muted-foreground) !important;
            margin-bottom: 0.5rem;
        }

        .hwid-value {
            font-family: var(--font-mono) !important;
            font-size: 1rem !important;
            color: var(--foreground) !important;
            word-break: break-all;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background: var(--muted);
            border-radius: calc(var(--radius) - 2px);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .hwid-value:hover {
            background: var(--input);
            transform: translateY(-1px);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem !important;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--muted-foreground);
            animation: pulse 2s infinite;
        }

        .status-dot.validating {
            background: var(--primary);
        }

        .status-dot.success {
            background: var(--accent);
            animation: none;
        }

        .status-dot.error {
            background: var(--destructive);
            animation: none;
        }

        /* Support buttons */
        .support-section {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .support-btn {
            padding: 0.75rem 1.5rem;
            background: transparent !important;
            border: 1px solid var(--border) !important;
            border-radius: var(--radius) !important;
            color: var(--muted-foreground) !important;
            font-size: 0.875rem !important;
            cursor: pointer;
            transition: all 0.15s ease;
            text-decoration: none;
        }

        .support-btn:hover {
            border-color: var(--primary);
            color: var(--primary);
            transform: scale(1.05);
        }

        /* Responsive design */
        @media (max-width: 640px) {
            .steam-container {
                padding: 1rem;
            }
            
            .steam-card {
                padding: 2rem;
            }
            
            .steam-title {
                font-size: 2rem !important;
            }
            
            .support-section {
                flex-direction: column;
            }
        }

        /* Toast notifications */
        .toast {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1rem 1.5rem;
            color: var(--foreground);
            box-shadow: 0 10px 15px 0 rgba(0, 0, 0, 0.5);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            border-color: var(--accent);
            background: linear-gradient(135deg, var(--accent) 0%, var(--card) 100%);
        }

        .toast.error {
            border-color: var(--destructive);
            background: linear-gradient(135deg, var(--destructive) 0%, var(--card) 100%);
        }
    </style>
</head>
<body>
    <div class="steam-container">
        <!-- Header -->
        <div class="steam-header">
            <h1 class="steam-title">🎮 STEAM TOOLS</h1>
            <p class="steam-subtitle">License Activation</p>
        </div>

        <!-- Main Card -->
        <div class="steam-card">
            <!-- Logo Area -->
            <div class="steam-logo">
                <div class="steam-logo-icon">
                    <i data-lucide="gamepad-2"></i>
                </div>
                <div class="steam-logo-text">Steam Tools</div>
            </div>

            <!-- License Input -->
            <div class="license-section">
                <label class="license-label">License Key</label>
                <input 
                    type="text" 
                    class="license-input" 
                    id="licenseInput"
                    placeholder="XXXX-XXXX-XXXX-XXXX"
                    maxlength="19"
                >
            </div>

            <!-- Activate Button -->
            <button class="activate-btn" id="activateBtn">
                <span>Activate License</span>
                <div class="spinner"></div>
            </button>

            <!-- Progress Bar -->
            <div class="progress-section">
                <div class="progress-label">
                    <span>Activation Progress</span>
                    <span id="progressText">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
            </div>

            <!-- HWID Section -->
            <div class="hwid-section">
                <div class="hwid-label">Hardware ID</div>
                <div class="hwid-value" id="hwidValue" onclick="copyHWID()">
                    ABC123-DEF456-GHI789-JKL012
                </div>
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">Ready</span>
                </div>
            </div>

            <!-- Support Buttons -->
            <div class="support-section">
                <a href="#" class="support-btn">
                    <i data-lucide="help-circle" style="width: 16px; height: 16px; margin-right: 0.5rem;"></i>
                    Help
                </a>
                <a href="#" class="support-btn">
                    <i data-lucide="message-circle" style="width: 16px; height: 16px; margin-right: 0.5rem;"></i>
                    Support
                </a>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer"></div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // License input formatting
        const licenseInput = document.getElementById('licenseInput');
        const activateBtn = document.getElementById('activateBtn');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');

        // Format license key input
        licenseInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
            let formatted = value.match(/.{1,4}/g)?.join('-') || value;
            if (formatted.length > 19) formatted = formatted.substring(0, 19);
            e.target.value = formatted;
            
            // Validate format
            const isValid = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/.test(formatted);
            e.target.classList.toggle('success', isValid);
            e.target.classList.toggle('error', formatted.length === 19 && !isValid);
            
            activateBtn.disabled = !isValid;
        });

        // Activation process
        activateBtn.addEventListener('click', async function() {
            if (this.disabled) return;
            
            this.disabled = true;
            this.classList.add('loading');
            
            updateStatus('validating', 'Validating license...');
            
            // Simulate activation process
            await simulateActivation();
        });

        async function simulateActivation() {
            const steps = [
                { progress: 20, status: 'Connecting to server...' },
                { progress: 40, status: 'Verifying license key...' },
                { progress: 60, status: 'Checking hardware ID...' },
                { progress: 80, status: 'Activating license...' },
                { progress: 100, status: 'License activated successfully!' }
            ];
            
            for (let i = 0; i < steps.length; i++) {
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const step = steps[i];
                updateProgress(step.progress);
                updateStatus(i === steps.length - 1 ? 'success' : 'validating', step.status);
                
                if (i === steps.length - 1) {
                    activateBtn.classList.remove('loading');
                    activateBtn.innerHTML = '<i data-lucide="check" style="width: 20px; height: 20px; margin-right: 0.5rem;"></i>Activated';
                    activateBtn.style.background = 'var(--steam-gradient-success)';
                    showToast('License activated successfully!', 'success');
                    lucide.createIcons();
                }
            }
        }

        function updateProgress(percent) {
            progressFill.style.width = percent + '%';
            progressText.textContent = percent + '%';
        }

        function updateStatus(type, message) {
            statusDot.className = 'status-dot ' + type;
            statusText.textContent = message;
        }

        function copyHWID() {
            const hwidValue = document.getElementById('hwidValue').textContent;
            navigator.clipboard.writeText(hwidValue).then(() => {
                showToast('Hardware ID copied to clipboard!', 'success');
            });
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i data-lucide="${type === 'success' ? 'check-circle' : 'info'}" style="width: 16px; height: 16px;"></i>
                    ${message}
                </div>
            `;
            
            document.getElementById('toastContainer').appendChild(toast);
            lucide.createIcons();
            
            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // Initialize
        updateStatus('ready', 'Ready');
    </script>
</body>
</html>