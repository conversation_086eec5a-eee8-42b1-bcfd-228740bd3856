@echo off
setlocal enabledelayedexpansion
title Steam Tools Downloader v2 - Complete Build System
color 0A

:MAIN_MENU
cls
echo ========================================
echo Steam Tools Downloader v2 - Build Menu
echo ========================================
echo.
echo Choose your build option:
echo.
echo 1. Quick Build (Automated)
echo 2. Test Environment First
echo 3. Manual Build (Step by step)
echo 4. Clean Build (Remove all build files)
echo 5. Install Dependencies Only
echo 6. Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto QUICK_BUILD
if "%choice%"=="2" goto TEST_ENV
if "%choice%"=="3" goto MANUAL_BUILD
if "%choice%"=="4" goto CLEAN_BUILD
if "%choice%"=="5" goto INSTALL_DEPS
if "%choice%"=="6" goto EXIT
goto MAIN_MENU

:QUICK_BUILD
cls
echo ========================================
echo Quick Build - Automated Process
echo ========================================
echo.
echo This will automatically:
echo - Install dependencies
echo - Build the executable
echo - Create distribution package
echo.
pause

echo Starting automated build...
python build_exe.py

if errorlevel 1 (
    echo.
    echo Build failed! Check error messages above.
    pause
    goto MAIN_MENU
) else (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo Distribution package created:
    echo SteamToolsDownloader_v2_Distribution\
    echo.
    echo Ready to distribute!
    pause
    goto MAIN_MENU
)

:TEST_ENV
cls
echo ========================================
echo Testing Build Environment
echo ========================================
echo.
echo This will check if your system is ready for building...
echo.
pause

python test_build.py

echo.
echo Test completed. Check results above.
pause
goto MAIN_MENU

:MANUAL_BUILD
cls
echo ========================================
echo Manual Build Process
echo ========================================
echo.
echo This will guide you through each step manually.
echo.
pause

echo Step 1: Installing PyInstaller...
pip install pyinstaller
if errorlevel 1 (
    echo Failed to install PyInstaller!
    pause
    goto MAIN_MENU
)

echo.
echo Step 2: Installing application dependencies...
pip install -r ../requirements.txt
if errorlevel 1 (
    echo Warning: Some dependencies may have failed
)

echo.
echo Step 3: Installing build dependencies...
pip install -r build_requirements.txt
if errorlevel 1 (
    echo Warning: Some build dependencies may have failed
)

echo.
echo Step 4: Copying keyauth module...
if exist "keyauth.py" del "keyauth.py"
copy "..\..\src\keyauth.py" "keyauth.py"
if errorlevel 1 (
    echo Failed to copy keyauth.py!
    pause
    goto MAIN_MENU
)

echo.
echo Step 5: Cleaning previous builds...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

echo.
echo Step 6: Building executable...
echo This may take several minutes...
pyinstaller steam_tools_gui.spec --clean --noconfirm

if errorlevel 1 (
    echo Build failed!
    pause
    goto MAIN_MENU
)

echo.
echo Step 7: Creating distribution package...
if exist "SteamToolsDownloader_v2_Distribution" rmdir /s /q "SteamToolsDownloader_v2_Distribution"
mkdir "SteamToolsDownloader_v2_Distribution"

copy "dist\SteamToolsDownloader_v2.exe" "SteamToolsDownloader_v2_Distribution\"
copy "..\README.md" "SteamToolsDownloader_v2_Distribution\"

echo @echo off > "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo title Steam Tools Downloader v2 >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo echo Starting Steam Tools Downloader v2... >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo echo. >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo SteamToolsDownloader_v2.exe >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo echo. >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo echo Application closed. >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo pause >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"

echo.
echo Step 8: Cleaning up temporary files...
del "keyauth.py"
rmdir /s /q "build"

echo.
echo ========================================
echo MANUAL BUILD COMPLETED!
echo ========================================
echo.
echo Distribution package created successfully!
pause
goto MAIN_MENU

:CLEAN_BUILD
cls
echo ========================================
echo Clean Build Environment
echo ========================================
echo.
echo This will remove all build files and start fresh.
echo.
echo WARNING: This will delete:
echo - build\ directory
echo - dist\ directory  
echo - SteamToolsDownloader_v2_Distribution\ directory
echo - Any temporary files
echo.
set /p confirm="Are you sure? (y/N): "

if /i not "%confirm%"=="y" goto MAIN_MENU

echo.
echo Cleaning build environment...

if exist "build" (
    rmdir /s /q "build"
    echo Removed build\ directory
)

if exist "dist" (
    rmdir /s /q "dist"
    echo Removed dist\ directory
)

if exist "SteamToolsDownloader_v2_Distribution" (
    rmdir /s /q "SteamToolsDownloader_v2_Distribution"
    echo Removed distribution directory
)

if exist "keyauth.py" (
    del "keyauth.py"
    echo Removed temporary keyauth.py
)

if exist "*.pyc" (
    del "*.pyc"
    echo Removed Python cache files
)

echo.
echo Clean completed!
pause
goto MAIN_MENU

:INSTALL_DEPS
cls
echo ========================================
echo Install Dependencies Only
echo ========================================
echo.
echo This will install all required dependencies without building.
echo.
pause

echo Installing PyInstaller...
pip install pyinstaller

echo.
echo Installing application dependencies...
pip install -r ../requirements.txt

echo.
echo Installing build dependencies...
pip install -r build_requirements.txt

echo.
echo Dependencies installation completed!
echo.
echo You can now run a build using option 1 or 3.
pause
goto MAIN_MENU

:EXIT
echo.
echo Goodbye!
exit /b 0
