#!/usr/bin/env python3
"""
Test script to verify the build environment and dependencies
"""

import sys
import os
import subprocess
import importlib
from pathlib import Path

def test_python():
    """Test Python installation"""
    print("Testing Python installation...")
    version = sys.version_info
    print(f"  Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("  ❌ Python 3.7+ required")
        return False
    else:
        print("  ✅ Python version OK")
        return True

def test_pip():
    """Test pip installation"""
    print("\nTesting pip installation...")
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"  ✅ pip version: {result.stdout.strip()}")
            return True
        else:
            print("  ❌ pip not working")
            return False
    except:
        print("  ❌ pip not found")
        return False

def test_module(module_name, package_name=None):
    """Test if a module can be imported"""
    try:
        importlib.import_module(module_name)
        print(f"  ✅ {module_name}")
        return True
    except ImportError:
        package = package_name or module_name
        print(f"  ❌ {module_name} (install with: pip install {package})")
        return False

def test_dependencies():
    """Test required dependencies"""
    print("\nTesting dependencies...")
    
    modules = [
        ("tkinter", "tkinter (built-in)"),
        ("requests", "requests"),
        ("Crypto", "pycryptodome"),
        ("win32api", "pywin32"),
        ("json", "json (built-in)"),
        ("threading", "threading (built-in)"),
        ("hashlib", "hashlib (built-in)"),
        ("winreg", "winreg (built-in)"),
        ("pathlib", "pathlib (built-in)"),
        ("time", "time (built-in)"),
    ]
    
    all_ok = True
    for module, package in modules:
        if not test_module(module, package):
            all_ok = False
    
    return all_ok

def test_pyinstaller():
    """Test PyInstaller installation"""
    print("\nTesting PyInstaller...")
    try:
        result = subprocess.run([sys.executable, "-m", "PyInstaller", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"  ✅ PyInstaller version: {result.stdout.strip()}")
            return True
        else:
            print("  ❌ PyInstaller not working")
            return False
    except:
        print("  ❌ PyInstaller not found (install with: pip install pyinstaller)")
        return False

def test_source_files():
    """Test if source files exist"""
    print("\nTesting source files...")
    
    files = [
        ("../steam_tools_gui.py", "Main GUI application"),
        ("../../src/keyauth.py", "Keyauth module"),
        ("../requirements.txt", "Requirements file"),
        ("steam_tools_gui.spec", "PyInstaller spec file"),
        ("version_info.txt", "Version info file"),
    ]
    
    all_ok = True
    for file_path, description in files:
        path = Path(file_path)
        if path.exists():
            print(f"  ✅ {description}: {file_path}")
        else:
            print(f"  ❌ {description}: {file_path} (NOT FOUND)")
            all_ok = False
    
    return all_ok

def test_build_environment():
    """Test build environment"""
    print("\nTesting build environment...")
    
    # Check current directory
    current_dir = Path.cwd()
    print(f"  Current directory: {current_dir}")
    
    if current_dir.name != "pack":
        print("  ⚠️  Warning: Should be run from the 'pack' directory")
    else:
        print("  ✅ Running from correct directory")
    
    # Check write permissions
    try:
        test_file = Path("test_write.tmp")
        test_file.write_text("test")
        test_file.unlink()
        print("  ✅ Write permissions OK")
        return True
    except:
        print("  ❌ No write permissions in current directory")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print(" Steam Tools Downloader v2 - Build Environment Test")
    print("=" * 60)
    
    tests = [
        ("Python Installation", test_python),
        ("Pip Installation", test_pip),
        ("Dependencies", test_dependencies),
        ("PyInstaller", test_pyinstaller),
        ("Source Files", test_source_files),
        ("Build Environment", test_build_environment),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ Error during {test_name}: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print(" TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Ready to build.")
        print("\nNext steps:")
        print("  1. Run 'build.bat' for automated build")
        print("  2. Or run 'python build_exe.py' for detailed build")
        return True
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix issues before building.")
        print("\nCommon solutions:")
        print("  - Install missing dependencies: pip install -r build_requirements.txt")
        print("  - Ensure you're in the correct directory")
        print("  - Check file paths and permissions")
        return False

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
