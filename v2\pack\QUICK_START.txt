========================================
Steam Tools Downloader v2 - Quick Start
========================================

FASTEST WAY TO BUILD:
1. Double-click "build_complete.bat"
2. Choose option 1 (Quick Build)
3. Wait for completion
4. Find your executable in "SteamToolsDownloader_v2_Distribution" folder

ALTERNATIVE METHODS:
- build.bat (Simple automated build)
- build_manual.bat (Manual step-by-step)
- python build_exe.py (Python script with detailed logging)

TESTING:
- python test_build.py (Check if environment is ready)

REQUIREMENTS:
- Python 3.7+ installed
- Internet connection
- Windows 7 or later

OUTPUT:
- Single executable file (15-25 MB)
- No Python installation required on target machines
- Ready to distribute

TROUBLESHOOTING:
- If build fails, run test_build.py first
- Check README_PACKAGING.md for detailed help
- Ensure all source files are present

========================================
