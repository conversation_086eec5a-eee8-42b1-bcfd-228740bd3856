@echo off
title Steam Tools Downloader v2 - Manual Build
echo ========================================
echo Steam Tools Downloader v2 - Manual Build
echo ========================================
echo.

echo Step 1: Installing PyInstaller...
pip install pyinstaller
if errorlevel 1 (
    echo Failed to install PyInstaller
    pause
    exit /b 1
)

echo.
echo Step 2: Installing dependencies...
pip install -r ../requirements.txt
if errorlevel 1 (
    echo Warning: Some dependencies may have failed to install
)

echo.
echo Step 3: Copying keyauth.py...
copy "..\..\src\keyauth.py" "keyauth.py"
if errorlevel 1 (
    echo Failed to copy keyauth.py
    pause
    exit /b 1
)

echo.
echo Step 4: Building executable (this may take a few minutes)...
pyinstaller --onefile --windowed --name "SteamToolsDownloader_v2" --add-data "keyauth.py;." --hidden-import "keyauth" --hidden-import "tkinter" --hidden-import "tkinter.ttk" --hidden-import "tkinter.filedialog" --hidden-import "tkinter.messagebox" --hidden-import "tkinter.scrolledtext" --hidden-import "requests" --hidden-import "Crypto.Cipher.AES" --hidden-import "Crypto.Hash.SHA256" --hidden-import "Crypto.Util.Padding" --version-file "version_info.txt" "../steam_tools_gui.py"

if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Step 5: Creating distribution folder...
if exist "SteamToolsDownloader_v2_Distribution" rmdir /s /q "SteamToolsDownloader_v2_Distribution"
mkdir "SteamToolsDownloader_v2_Distribution"

copy "dist\SteamToolsDownloader_v2.exe" "SteamToolsDownloader_v2_Distribution\"
copy "..\README.md" "SteamToolsDownloader_v2_Distribution\"

echo @echo off > "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo title Steam Tools Downloader v2 >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo echo Starting Steam Tools Downloader v2... >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo echo. >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo SteamToolsDownloader_v2.exe >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo echo. >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo echo Application closed. >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"
echo pause >> "SteamToolsDownloader_v2_Distribution\Launch_SteamTools.bat"

echo.
echo Step 6: Cleaning up...
del "keyauth.py"
rmdir /s /q "build"

echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Distribution package created in:
echo SteamToolsDownloader_v2_Distribution\
echo.
echo Files included:
echo - SteamToolsDownloader_v2.exe (Main application)
echo - Launch_SteamTools.bat (Launcher)
echo - README.md (Documentation)
echo.
echo Ready to distribute!
echo.
pause
