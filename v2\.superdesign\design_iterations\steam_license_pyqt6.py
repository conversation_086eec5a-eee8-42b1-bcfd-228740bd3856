import sys
import async<PERSON>
import random
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                            QProgressBar, QFrame, QGridLayout, QTextEdit)
from PyQt6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, pyqtSignal, QThread
from PyQt6.QtGui import QFont, QPalette, QColor, QPixmap, QPainter, QBrush, QLinearGradient
import time

class ConnectionStatusWidget(QFrame):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_animations()
        
    def setup_ui(self):
        self.setFixedSize(280, 80)
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #1b2838, stop:1 #16202d);
                border: 1px solid #2a475e;
                border-radius: 6px;
                padding: 12px;
            }
        """)
        
        layout = QHBoxLayout(self)
        
        # Connection indicator
        self.connection_dot = QLabel()
        self.connection_dot.setFixedSize(10, 10)
        self.connection_dot.setStyleSheet("""
            QLabel {
                background-color: #00d26a;
                border-radius: 5px;
                border: none;
            }
        """)
        
        # Connection text
        text_layout = QVBoxLayout()
        self.connection_text = QLabel("Connected to License Server")
        self.connection_text.setStyleSheet("""
            QLabel {
                color: #c7d5e0;
                font-size: 14px;
                font-weight: 500;
                background: transparent;
                border: none;
            }
        """)
        
        self.connection_details = QLabel("auth.steamtools.com • 24ms")
        self.connection_details.setStyleSheet("""
            QLabel {
                color: #8b98a5;
                font-size: 12px;
                background: transparent;
                border: none;
            }
        """)
        
        text_layout.addWidget(self.connection_text)
        text_layout.addWidget(self.connection_details)
        text_layout.setSpacing(2)
        
        layout.addWidget(self.connection_dot)
        layout.addLayout(text_layout)
        layout.setSpacing(12)
        
    def setup_animations(self):
        # Pulsing animation for connection dot
        self.pulse_timer = QTimer()
        self.pulse_timer.timeout.connect(self.pulse_dot)
        self.pulse_timer.start(2000)
        self.pulse_state = False
        
    def pulse_dot(self):
        if self.pulse_state:
            self.connection_dot.setStyleSheet("""
                QLabel {
                    background-color: #00d26a;
                    border-radius: 5px;
                    border: none;
                }
            """)
        else:
            self.connection_dot.setStyleSheet("""
                QLabel {
                    background-color: rgba(0, 210, 106, 0.6);
                    border-radius: 5px;
                    border: none;
                }
            """)
        self.pulse_state = not self.pulse_state
        
    def set_connecting(self):
        self.connection_dot.setStyleSheet("""
            QLabel {
                background-color: #ffb800;
                border-radius: 5px;
                border: none;
            }
        """)
        self.connection_text.setText("Connecting to License Server")
        self.connection_details.setText("Establishing secure connection...")

class ServerStatusPanel(QFrame):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_metrics_timer()
        
    def setup_ui(self):
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #2a475e, stop:1 #1b2838);
                border: 1px solid #2a475e;
                border-radius: 6px;
                padding: 16px;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        title = QLabel("🖥️ Authentication Server")
        title.setStyleSheet("""
            QLabel {
                color: #c7d5e0;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        """)
        
        self.server_dot = QLabel()
        self.server_dot.setFixedSize(8, 8)
        self.server_dot.setStyleSheet("""
            QLabel {
                background-color: #00d26a;
                border-radius: 4px;
                border: none;
            }
        """)
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        header_layout.addWidget(self.server_dot)
        
        # Metrics
        metrics_layout = QGridLayout()
        
        # Latency
        latency_label = QLabel("LATENCY")
        latency_label.setStyleSheet("""
            QLabel {
                color: #8b98a5;
                font-size: 12px;
                font-weight: 500;
                background: transparent;
                border: none;
            }
        """)
        
        self.latency_value = QLabel("24ms")
        self.latency_value.setStyleSheet("""
            QLabel {
                color: #00d26a;
                font-size: 14px;
                font-weight: 600;
                font-family: 'JetBrains Mono', monospace;
                background: transparent;
                border: none;
            }
        """)
        
        # Uptime
        uptime_label = QLabel("UPTIME")
        uptime_label.setStyleSheet("""
            QLabel {
                color: #8b98a5;
                font-size: 12px;
                font-weight: 500;
                background: transparent;
                border: none;
            }
        """)
        
        self.uptime_value = QLabel("99.9%")
        self.uptime_value.setStyleSheet("""
            QLabel {
                color: #00d26a;
                font-size: 14px;
                font-weight: 600;
                font-family: 'JetBrains Mono', monospace;
                background: transparent;
                border: none;
            }
        """)
        
        metrics_layout.addWidget(latency_label, 0, 0)
        metrics_layout.addWidget(self.latency_value, 1, 0)
        metrics_layout.addWidget(uptime_label, 0, 1)
        metrics_layout.addWidget(self.uptime_value, 1, 1)
        
        # Data flow indicator
        self.data_flow = QFrame()
        self.data_flow.setFixedHeight(2)
        self.data_flow.setStyleSheet("""
            QFrame {
                background-color: #171a21;
                border-radius: 1px;
                border: none;
            }
        """)
        
        layout.addLayout(header_layout)
        layout.addLayout(metrics_layout)
        layout.addWidget(self.data_flow)
        layout.setSpacing(12)
        
    def setup_metrics_timer(self):
        self.metrics_timer = QTimer()
        self.metrics_timer.timeout.connect(self.update_metrics)
        self.metrics_timer.start(3000)
        
    def update_metrics(self):
        latencies = [18, 24, 31, 19, 27, 22, 29]
        uptimes = ['99.9%', '99.8%', '100%', '99.7%']
        
        random_latency = random.choice(latencies)
        random_uptime = random.choice(uptimes)
        
        self.latency_value.setText(f"{random_latency}ms")
        self.uptime_value.setText(random_uptime)

class ActivationWorker(QThread):
    progress_updated = pyqtSignal(int, str)
    activation_complete = pyqtSignal()
    
    def run(self):
        steps = [
            (20, "Connecting to authentication server..."),
            (40, "Verifying license key..."),
            (60, "Checking hardware ID..."),
            (80, "Activating license..."),
            (100, "License activated successfully!")
        ]
        
        for progress, status in steps:
            time.sleep(1)
            self.progress_updated.emit(progress, status)
            
        self.activation_complete.emit()

class SteamLicenseActivator(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_styles()
        self.setup_connections()
        
    def setup_ui(self):
        self.setWindowTitle("Steam Tools - License Activation")
        self.setFixedSize(600, 900)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # Connection status (top right)
        self.connection_status = ConnectionStatusWidget()
        status_layout = QHBoxLayout()
        status_layout.addStretch()
        status_layout.addWidget(self.connection_status)
        main_layout.addLayout(status_layout)
        
        # Header
        header_layout = QVBoxLayout()
        header_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.title = QLabel("🎮 STEAM TOOLS")
        self.title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.subtitle = QLabel("License Activation")
        self.subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        header_layout.addWidget(self.title)
        header_layout.addWidget(self.subtitle)
        header_layout.setSpacing(8)
        
        # Main card
        self.main_card = QFrame()
        card_layout = QVBoxLayout(self.main_card)
        card_layout.setSpacing(24)
        card_layout.setContentsMargins(48, 48, 48, 48)
        
        # Server status panel
        self.server_status = ServerStatusPanel()
        
        # Logo area
        logo_frame = QFrame()
        logo_layout = QVBoxLayout(logo_frame)
        logo_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.logo_icon = QLabel("🎮")
        self.logo_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.logo_text = QLabel("Steam Tools")
        self.logo_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        logo_layout.addWidget(self.logo_icon)
        logo_layout.addWidget(self.logo_text)
        logo_layout.setSpacing(16)
        
        # License input
        license_layout = QVBoxLayout()
        
        self.license_label = QLabel("License Key")
        
        self.license_input = QLineEdit()
        self.license_input.setPlaceholderText("XXXX-XXXX-XXXX-XXXX")
        self.license_input.setMaxLength(19)
        self.license_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        license_layout.addWidget(self.license_label)
        license_layout.addWidget(self.license_input)
        license_layout.setSpacing(8)
        
        # Activate button
        self.activate_btn = QPushButton("Activate License")
        self.activate_btn.setEnabled(False)
        
        # Progress section
        progress_layout = QVBoxLayout()
        
        progress_header = QHBoxLayout()
        progress_title = QLabel("Activation Progress")
        self.progress_text = QLabel("0%")
        progress_header.addWidget(progress_title)
        progress_header.addStretch()
        progress_header.addWidget(self.progress_text)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        
        progress_layout.addLayout(progress_header)
        progress_layout.addWidget(self.progress_bar)
        progress_layout.setSpacing(8)
        
        # HWID section
        hwid_frame = QFrame()
        hwid_layout = QVBoxLayout(hwid_frame)
        
        hwid_label = QLabel("Hardware ID")
        
        self.hwid_value = QTextEdit("ABC123-DEF456-GHI789-JKL012")
        self.hwid_value.setFixedHeight(50)
        self.hwid_value.setReadOnly(True)
        
        status_layout = QHBoxLayout()
        self.status_dot = QLabel()
        self.status_dot.setFixedSize(8, 8)
        self.status_text = QLabel("Ready")
        
        status_layout.addWidget(self.status_dot)
        status_layout.addWidget(self.status_text)
        status_layout.addStretch()
        status_layout.setSpacing(8)
        
        hwid_layout.addWidget(hwid_label)
        hwid_layout.addWidget(self.hwid_value)
        hwid_layout.addLayout(status_layout)
        hwid_layout.setSpacing(12)
        
        # Support buttons
        support_layout = QHBoxLayout()
        support_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.help_btn = QPushButton("❓ Help")
        self.support_btn = QPushButton("💬 Support")
        
        support_layout.addWidget(self.help_btn)
        support_layout.addWidget(self.support_btn)
        support_layout.setSpacing(16)
        
        # Add all components to card
        card_layout.addWidget(self.server_status)
        card_layout.addWidget(logo_frame)
        card_layout.addLayout(license_layout)
        card_layout.addWidget(self.activate_btn)
        card_layout.addLayout(progress_layout)
        card_layout.addWidget(hwid_frame)
        card_layout.addLayout(support_layout)
        
        # Add to main layout
        main_layout.addLayout(header_layout)
        main_layout.addWidget(self.main_card)
        main_layout.addStretch()
        
    def setup_styles(self):
        # Main window style
        self.setStyleSheet("""
            QMainWindow {
                background-color: #0e1419;
                color: #c7d5e0;
            }
            
            QWidget {
                background-color: #0e1419;
                color: #c7d5e0;
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
        """)
        
        # Title styles
        self.title.setStyleSheet("""
            QLabel {
                font-size: 40px;
                font-weight: 700;
                color: #66c0f4;
                background: transparent;
                border: none;
            }
        """)
        
        self.subtitle.setStyleSheet("""
            QLabel {
                font-size: 18px;
                color: #8b98a5;
                font-weight: 400;
                background: transparent;
                border: none;
            }
        """)
        
        # Main card style
        self.main_card.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #1b2838, stop:1 #16202d);
                border: 1px solid #2a475e;
                border-radius: 6px;
            }
        """)
        
        # Logo styles
        self.logo_icon.setStyleSheet("""
            QLabel {
                font-size: 64px;
                background: transparent;
                border: none;
            }
        """)
        
        self.logo_text.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: 600;
                color: #66c0f4;
                background: transparent;
                border: none;
            }
        """)
        
        # License input styles
        self.license_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 500;
                color: #c7d5e0;
                background: transparent;
                border: none;
            }
        """)
        
        self.license_input.setStyleSheet("""
            QLineEdit {
                padding: 16px;
                background-color: #1b2838;
                border: 2px solid #2a475e;
                border-radius: 6px;
                color: #c7d5e0;
                font-family: 'JetBrains Mono', monospace;
                font-size: 18px;
                letter-spacing: 0.1em;
            }
            
            QLineEdit:focus {
                border-color: #66c0f4;
                background-color: #1b2838;
            }
            
            QLineEdit[valid="true"] {
                border-color: #4c6b22;
            }
            
            QLineEdit[valid="false"] {
                border-color: #cd412b;
            }
        """)
        
        # Activate button style
        self.activate_btn.setStyleSheet("""
            QPushButton {
                padding: 16px 32px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #66c0f4, stop:1 #4c9eff);
                border: none;
                border-radius: 6px;
                color: #0e1419;
                font-size: 18px;
                font-weight: 600;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #7dd3ff, stop:1 #66c0f4);
            }
            
            QPushButton:disabled {
                background-color: #2a475e;
                color: #8b98a5;
            }
            
            QPushButton[activated="true"] {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #4c6b22, stop:1 #5c7e10);
                color: #c7d5e0;
            }
        """)
        
        # Progress bar style
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                background-color: #171a21;
                border-radius: 4px;
                height: 8px;
            }
            
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #66c0f4, stop:1 #4c9eff);
                border-radius: 4px;
            }
        """)
        
        # HWID section styles
        hwid_frame = self.findChild(QFrame)
        if hwid_frame:
            hwid_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                        stop:0 #2a475e, stop:1 #1b2838);
                    border: 1px solid #2a475e;
                    border-radius: 6px;
                    padding: 24px;
                }
            """)
        
        self.hwid_value.setStyleSheet("""
            QTextEdit {
                background-color: #171a21;
                border: none;
                border-radius: 4px;
                color: #c7d5e0;
                font-family: 'JetBrains Mono', monospace;
                font-size: 16px;
                padding: 12px;
            }
        """)
        
        # Status dot
        self.status_dot.setStyleSheet("""
            QLabel {
                background-color: #8b98a5;
                border-radius: 4px;
                border: none;
            }
        """)
        
        self.status_text.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #c7d5e0;
                background: transparent;
                border: none;
            }
        """)
        
        # Support buttons
        support_btn_style = """
            QPushButton {
                padding: 12px 24px;
                background: transparent;
                border: 1px solid #2a475e;
                border-radius: 6px;
                color: #8b98a5;
                font-size: 14px;
            }
            
            QPushButton:hover {
                border-color: #66c0f4;
                color: #66c0f4;
            }
        """
        
        self.help_btn.setStyleSheet(support_btn_style)
        self.support_btn.setStyleSheet(support_btn_style)
        
    def setup_connections(self):
        self.license_input.textChanged.connect(self.validate_license)
        self.activate_btn.clicked.connect(self.start_activation)
        self.hwid_value.mousePressEvent = self.copy_hwid
        
    def validate_license(self, text):
        # Format input
        value = ''.join(c.upper() for c in text if c.isalnum())
        formatted = '-'.join(value[i:i+4] for i in range(0, len(value), 4))
        
        if len(formatted) > 19:
            formatted = formatted[:19]
            
        self.license_input.blockSignals(True)
        self.license_input.setText(formatted)
        self.license_input.blockSignals(False)
        
        # Validate format
        is_valid = len(formatted) == 19 and formatted.count('-') == 3
        
        if is_valid:
            self.license_input.setProperty("valid", "true")
            self.activate_btn.setEnabled(True)
        elif len(formatted) == 19:
            self.license_input.setProperty("valid", "false")
            self.activate_btn.setEnabled(False)
        else:
            self.license_input.setProperty("valid", None)
            self.activate_btn.setEnabled(False)
            
        self.license_input.style().polish(self.license_input)
        
    def start_activation(self):
        self.activate_btn.setEnabled(False)
        self.activate_btn.setText("Activating...")
        
        self.update_status("validating", "Validating license...")
        
        # Start activation worker
        self.worker = ActivationWorker()
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.activation_complete.connect(self.activation_finished)
        self.worker.start()
        
    def update_progress(self, progress, status):
        self.progress_bar.setValue(progress)
        self.progress_text.setText(f"{progress}%")
        
        if progress == 100:
            self.update_status("success", status)
        else:
            self.update_status("validating", status)
            
    def update_status(self, status_type, message):
        if status_type == "validating":
            color = "#66c0f4"
        elif status_type == "success":
            color = "#4c6b22"
        elif status_type == "error":
            color = "#cd412b"
        else:
            color = "#8b98a5"
            
        self.status_dot.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                border-radius: 4px;
                border: none;
            }}
        """)
        
        self.status_text.setText(message)
        
    def activation_finished(self):
        self.activate_btn.setText("✓ Activated")
        self.activate_btn.setProperty("activated", "true")
        self.activate_btn.style().polish(self.activate_btn)
        
    def copy_hwid(self, event):
        hwid_text = self.hwid_value.toPlainText()
        clipboard = QApplication.clipboard()
        clipboard.setText(hwid_text)
        
        # Show temporary feedback
        original_text = self.status_text.text()
        self.status_text.setText("Hardware ID copied to clipboard!")
        QTimer.singleShot(2000, lambda: self.status_text.setText(original_text))

def main():
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle('Fusion')
    
    # Set dark palette
    palette = QPalette()
    palette.setColor(QPalette.ColorRole.Window, QColor(14, 20, 25))
    palette.setColor(QPalette.ColorRole.WindowText, QColor(199, 213, 224))
    palette.setColor(QPalette.ColorRole.Base, QColor(27, 40, 56))
    palette.setColor(QPalette.ColorRole.AlternateBase, QColor(42, 71, 94))
    palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(199, 213, 224))
    palette.setColor(QPalette.ColorRole.ToolTipText, QColor(199, 213, 224))
    palette.setColor(QPalette.ColorRole.Text, QColor(199, 213, 224))
    palette.setColor(QPalette.ColorRole.Button, QColor(27, 40, 56))
    palette.setColor(QPalette.ColorRole.ButtonText, QColor(199, 213, 224))
    palette.setColor(QPalette.ColorRole.BrightText, QColor(102, 192, 244))
    palette.setColor(QPalette.ColorRole.Link, QColor(102, 192, 244))
    palette.setColor(QPalette.ColorRole.Highlight, QColor(102, 192, 244))
    palette.setColor(QPalette.ColorRole.HighlightedText, QColor(14, 20, 25))
    app.setPalette(palette)
    
    window = SteamLicenseActivator()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()