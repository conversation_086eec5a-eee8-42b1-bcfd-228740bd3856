#!/usr/bin/env python3
"""
License Server Status Icons Generator
Creates modern status icons for different connection states
"""

import os
from PIL import Image, ImageDraw, ImageFont
import math

# Steam color palette
STEAM_BLUE = "#66c0f4"
STEAM_DARK_BLUE = "#1b2838"
STEAM_GREEN = "#4c9a2a"
STEAM_RED = "#cd412b"
STEAM_ORANGE = "#ffa500"
STEAM_WHITE = "#c7d5e0"

def hex_to_rgb(hex_color):
    """Convert hex color to RGB tuple"""
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

def create_connecting_icon(size):
    """Create animated spinner icon for connecting state"""
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    center = size // 2
    radius = int(size * 0.35)
    thickness = max(2, size // 16)
    
    # Create spinner with Steam blue
    color = hex_to_rgb(STEAM_BLUE)
    
    # Draw partial circle (spinner effect)
    for i in range(8):
        angle_start = i * 45
        angle_end = angle_start + 30
        
        # Fade effect - brighter at the "head" of the spinner
        alpha = int(255 * (i + 1) / 8)
        
        # Draw arc segment
        bbox = [center - radius, center - radius, center + radius, center + radius]
        draw.arc(bbox, angle_start, angle_end, fill=color + (alpha,), width=thickness)
    
    return img

def create_connected_icon(size):
    """Create checkmark icon for connected state"""
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    center = size // 2
    radius = int(size * 0.4)
    
    # Background circle
    bg_color = hex_to_rgb(STEAM_GREEN)
    draw.ellipse([center - radius, center - radius, 
                  center + radius, center + radius], 
                 fill=bg_color + (255,))
    
    # Checkmark
    check_color = hex_to_rgb(STEAM_WHITE)
    thickness = max(2, size // 12)
    
    # Checkmark coordinates (relative to center)
    check_size = radius * 0.6
    x1 = center - check_size * 0.3
    y1 = center
    x2 = center - check_size * 0.1
    y2 = center + check_size * 0.3
    x3 = center + check_size * 0.4
    y3 = center - check_size * 0.2
    
    # Draw checkmark lines
    draw.line([(x1, y1), (x2, y2)], fill=check_color + (255,), width=thickness)
    draw.line([(x2, y2), (x3, y3)], fill=check_color + (255,), width=thickness)
    
    return img

def create_disconnected_icon(size):
    """Create X icon for disconnected state"""
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    center = size // 2
    radius = int(size * 0.4)
    
    # Background circle
    bg_color = hex_to_rgb(STEAM_RED)
    draw.ellipse([center - radius, center - radius, 
                  center + radius, center + radius], 
                 fill=bg_color + (255,))
    
    # X mark
    x_color = hex_to_rgb(STEAM_WHITE)
    thickness = max(2, size // 12)
    x_size = radius * 0.5
    
    # X coordinates
    x1 = center - x_size
    y1 = center - x_size
    x2 = center + x_size
    y2 = center + x_size
    x3 = center + x_size
    y3 = center - x_size
    x4 = center - x_size
    y4 = center + x_size
    
    # Draw X lines
    draw.line([(x1, y1), (x2, y2)], fill=x_color + (255,), width=thickness)
    draw.line([(x3, y3), (x4, y4)], fill=x_color + (255,), width=thickness)
    
    return img

def create_error_icon(size):
    """Create warning triangle icon for error state"""
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    center = size // 2
    triangle_size = int(size * 0.7)
    
    # Triangle coordinates
    top_x = center
    top_y = center - triangle_size // 2
    left_x = center - triangle_size // 2
    left_y = center + triangle_size // 3
    right_x = center + triangle_size // 2
    right_y = center + triangle_size // 3
    
    # Background triangle
    bg_color = hex_to_rgb(STEAM_ORANGE)
    draw.polygon([(top_x, top_y), (left_x, left_y), (right_x, right_y)], 
                 fill=bg_color + (255,))
    
    # Exclamation mark
    excl_color = hex_to_rgb(STEAM_WHITE)
    thickness = max(2, size // 16)
    
    # Exclamation line
    line_top = center - triangle_size // 4
    line_bottom = center + triangle_size // 8
    draw.line([(center, line_top), (center, line_bottom)], 
              fill=excl_color + (255,), width=thickness)
    
    # Exclamation dot
    dot_radius = max(1, size // 24)
    dot_y = center + triangle_size // 4
    draw.ellipse([center - dot_radius, dot_y - dot_radius,
                  center + dot_radius, dot_y + dot_radius],
                 fill=excl_color + (255,))
    
    return img

def create_all_status_icons():
    """Create all status icons in multiple sizes"""
    sizes = [16, 24, 32, 48]
    icons = {
        'connecting': create_connecting_icon,
        'connected': create_connected_icon,
        'disconnected': create_disconnected_icon,
        'error': create_error_icon
    }
    
    # Create assets directory if it doesn't exist
    assets_dir = os.path.dirname(os.path.abspath(__file__))
    if not os.path.exists(assets_dir):
        os.makedirs(assets_dir)
    
    created_files = []
    
    for icon_name, icon_func in icons.items():
        for size in sizes:
            print(f"Creating {icon_name} icon ({size}x{size})...")
            icon = icon_func(size)
            
            # Save PNG file
            filename = f"status_{icon_name}_{size}x{size}.png"
            filepath = os.path.join(assets_dir, filename)
            icon.save(filepath, "PNG")
            created_files.append(filepath)
            print(f"Saved: {filepath}")
    
    return created_files

def create_preview_image():
    """Create a preview image showing all status icons"""
    sizes = [16, 24, 32, 48]
    icons = ['connecting', 'connected', 'disconnected', 'error']
    icon_funcs = {
        'connecting': create_connecting_icon,
        'connected': create_connected_icon,
        'disconnected': create_disconnected_icon,
        'error': create_error_icon
    }
    
    # Create preview canvas
    preview_width = 600
    preview_height = 400
    preview = Image.new('RGBA', (preview_width, preview_height), (40, 44, 52, 255))
    draw = ImageDraw.Draw(preview)
    
    # Add title
    try:
        title_font = ImageFont.truetype("arial.ttf", 20)
        label_font = ImageFont.truetype("arial.ttf", 12)
    except:
        title_font = ImageFont.load_default()
        label_font = ImageFont.load_default()
    
    title = "License Server Status Icons"
    title_bbox = draw.textbbox((0, 0), title, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    draw.text(((preview_width - title_width) // 2, 20), title, 
              fill=hex_to_rgb(STEAM_WHITE), font=title_font)
    
    # Draw icons in a grid
    start_y = 70
    row_height = 80
    col_width = preview_width // len(sizes)
    
    # Column headers (sizes)
    for i, size in enumerate(sizes):
        x = i * col_width + col_width // 2
        size_text = f"{size}x{size}"
        text_bbox = draw.textbbox((0, 0), size_text, font=label_font)
        text_width = text_bbox[2] - text_bbox[0]
        draw.text((x - text_width // 2, start_y - 25), size_text, 
                  fill=hex_to_rgb(STEAM_WHITE), font=label_font)
    
    # Draw icons
    for row, icon_name in enumerate(icons):
        # Row label
        y = start_y + row * row_height + row_height // 2
        draw.text((10, y - 6), icon_name.title(), 
                  fill=hex_to_rgb(STEAM_WHITE), font=label_font)
        
        # Icons for each size
        for col, size in enumerate(sizes):
            icon = icon_funcs[icon_name](size)
            x = col * col_width + col_width // 2 - size // 2
            y = start_y + row * row_height + (row_height - size) // 2
            preview.paste(icon, (x, y), icon)
    
    # Save preview
    preview_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "status_icons_preview.png")
    preview.save(preview_path, "PNG")
    print(f"Created preview: {preview_path}")
    
    return preview_path

def main():
    """Main function to create all status icons"""
    print("🔗 License Server Status Icons Generator")
    print("=" * 45)
    
    try:
        # Create all status icons
        created_files = create_all_status_icons()
        
        # Create preview image
        preview_path = create_preview_image()
        
        print(f"\n✅ Status icons creation completed successfully!")
        print(f"📁 Created {len(created_files)} icon files")
        print(f"🖼️  Preview image: {preview_path}")
        print("\nThe status icons are ready to be integrated into the application!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating status icons: {str(e)}")
        return False

if __name__ == "__main__":
    main()
