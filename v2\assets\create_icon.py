#!/usr/bin/env python3
"""
Steam Tools Icon Generator
Creates a professional Steam-themed application icon in multiple sizes
"""

import os
from PIL import Image, ImageDraw, ImageFont
import math

# Steam color palette
STEAM_BLUE = "#66c0f4"
STEAM_DARK_BLUE = "#1b2838"
STEAM_LIGHT_BLUE = "#4c9eff"
STEAM_ACCENT = "#2a475e"
STEAM_WHITE = "#c7d5e0"

def hex_to_rgb(hex_color):
    """Convert hex color to RGB tuple"""
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

def create_steam_icon(size):
    """Create a Steam-themed icon at the specified size"""
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Calculate dimensions based on size
    center = size // 2
    outer_radius = int(size * 0.45)
    inner_radius = int(size * 0.35)
    gear_radius = int(size * 0.25)
    
    # Colors
    bg_color = hex_to_rgb(STEAM_DARK_BLUE)
    accent_color = hex_to_rgb(STEAM_BLUE)
    light_color = hex_to_rgb(STEAM_LIGHT_BLUE)
    white_color = hex_to_rgb(STEAM_WHITE)
    
    # Draw outer circle (background)
    draw.ellipse([center - outer_radius, center - outer_radius, 
                  center + outer_radius, center + outer_radius], 
                 fill=bg_color + (255,))
    
    # Draw inner circle (accent)
    draw.ellipse([center - inner_radius, center - inner_radius, 
                  center + inner_radius, center + inner_radius], 
                 fill=accent_color + (255,))
    
    # Draw gear/activation symbol in center
    gear_points = 8
    gear_inner = int(gear_radius * 0.6)
    
    # Create gear shape
    gear_coords = []
    for i in range(gear_points * 2):
        angle = (i * math.pi) / gear_points
        if i % 2 == 0:
            # Outer points
            x = center + int(gear_radius * math.cos(angle))
            y = center + int(gear_radius * math.sin(angle))
        else:
            # Inner points
            x = center + int(gear_inner * math.cos(angle))
            y = center + int(gear_inner * math.sin(angle))
        gear_coords.extend([x, y])
    
    # Draw gear
    draw.polygon(gear_coords, fill=white_color + (255,))
    
    # Draw center circle
    center_radius = int(size * 0.08)
    draw.ellipse([center - center_radius, center - center_radius,
                  center + center_radius, center + center_radius],
                 fill=bg_color + (255,))
    
    # Add highlight effect
    highlight_radius = int(outer_radius * 0.8)
    highlight_offset = int(size * 0.1)
    
    # Create gradient-like highlight
    for i in range(3):
        alpha = 60 - (i * 20)
        highlight_r = highlight_radius - (i * 2)
        draw.ellipse([center - highlight_r - highlight_offset, 
                      center - highlight_r - highlight_offset,
                      center + highlight_r - highlight_offset, 
                      center + highlight_r - highlight_offset], 
                     fill=light_color + (alpha,))
    
    return img

def create_all_icon_sizes():
    """Create icon files in all required sizes"""
    sizes = [16, 32, 48, 64, 128, 256]
    
    # Create assets directory if it doesn't exist
    assets_dir = os.path.dirname(os.path.abspath(__file__))
    if not os.path.exists(assets_dir):
        os.makedirs(assets_dir)
    
    # Create individual PNG files for each size
    images = []
    for size in sizes:
        print(f"Creating {size}x{size} icon...")
        icon = create_steam_icon(size)
        
        # Save individual PNG
        png_path = os.path.join(assets_dir, f"steam_tools_icon_{size}x{size}.png")
        icon.save(png_path, "PNG")
        print(f"Saved: {png_path}")
        
        images.append(icon)
    
    # Create ICO file with all sizes
    ico_path = os.path.join(assets_dir, "steam_tools_icon.ico")
    images[0].save(ico_path, format='ICO', sizes=[(s, s) for s in sizes])
    print(f"Created multi-size ICO: {ico_path}")
    
    return ico_path

def create_preview_image():
    """Create a preview image showing all icon sizes"""
    sizes = [16, 32, 48, 64, 128, 256]
    
    # Create preview canvas
    preview_width = 800
    preview_height = 400
    preview = Image.new('RGBA', (preview_width, preview_height), (40, 44, 52, 255))
    
    # Add title
    draw = ImageDraw.Draw(preview)
    try:
        # Try to use a nice font
        title_font = ImageFont.truetype("arial.ttf", 24)
        size_font = ImageFont.truetype("arial.ttf", 12)
    except:
        # Fallback to default font
        title_font = ImageFont.load_default()
        size_font = ImageFont.load_default()
    
    # Draw title
    title = "Steam Tools Application Icon - All Sizes"
    title_bbox = draw.textbbox((0, 0), title, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    draw.text(((preview_width - title_width) // 2, 20), title, 
              fill=hex_to_rgb(STEAM_WHITE), font=title_font)
    
    # Draw icons in a row
    start_x = 50
    start_y = 80
    spacing = 120
    
    for i, size in enumerate(sizes):
        icon = create_steam_icon(size)
        
        # Calculate position
        x = start_x + (i * spacing)
        y = start_y + (128 - size) // 2  # Center vertically
        
        # Paste icon
        preview.paste(icon, (x, y), icon)
        
        # Add size label
        label = f"{size}x{size}"
        label_bbox = draw.textbbox((0, 0), label, font=size_font)
        label_width = label_bbox[2] - label_bbox[0]
        draw.text((x + (size - label_width) // 2, y + size + 10), 
                  label, fill=hex_to_rgb(STEAM_WHITE), font=size_font)
    
    # Save preview
    assets_dir = os.path.dirname(os.path.abspath(__file__))
    preview_path = os.path.join(assets_dir, "icon_preview.png")
    preview.save(preview_path, "PNG")
    print(f"Created preview: {preview_path}")
    
    return preview_path

def main():
    """Main function to create all icon assets"""
    print("🎮 Steam Tools Icon Generator")
    print("=" * 40)
    
    try:
        # Create all icon sizes
        ico_path = create_all_icon_sizes()
        
        # Create preview image
        preview_path = create_preview_image()
        
        print("\n✅ Icon creation completed successfully!")
        print(f"📁 Main ICO file: {ico_path}")
        print(f"🖼️  Preview image: {preview_path}")
        print("\nThe icon is ready to be integrated into the application!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating icons: {str(e)}")
        return False

if __name__ == "__main__":
    main()
