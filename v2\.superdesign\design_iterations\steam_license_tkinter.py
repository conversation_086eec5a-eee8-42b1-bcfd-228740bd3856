import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import threading
import time
import pyperclip
import re

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class SteamLicenseActivator:
    def __init__(self):
        # Create main window
        self.root = ctk.CTk()
        self.root.title("🎮 Steam Tools - License Activation")
        self.root.geometry("600x800")
        self.root.resizable(False, False)
        
        # Steam-inspired color scheme
        self.colors = {
            'bg_primary': '#0e1419',
            'bg_secondary': '#1b2838',
            'bg_tertiary': '#171a21',
            'accent_blue': '#66c0f4',
            'accent_green': '#4c6b22',
            'accent_red': '#cd412b',
            'text_primary': '#c7d5e0',
            'text_secondary': '#8b98a5',
            'border': '#2a475e'
        }
        
        # Configure root background
        self.root.configure(fg_color=self.colors['bg_primary'])
        
        # Initialize variables
        self.license_var = tk.StringVar()
        self.license_var.trace('w', self.on_license_change)
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="Ready")
        self.hwid = "ABC123-DEF456-GHI789-JKL012"
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main container with padding
        main_frame = ctk.CTkFrame(
            self.root,
            fg_color=self.colors['bg_primary'],
            corner_radius=0
        )
        main_frame.pack(fill="both", expand=True, padx=40, pady=40)
        
        # Header section
        self.create_header(main_frame)
        
        # Main card
        self.create_main_card(main_frame)
        
    def create_header(self, parent):
        header_frame = ctk.CTkFrame(
            parent,
            fg_color="transparent"
        )
        header_frame.pack(pady=(0, 30))
        
        # Title
        title_label = ctk.CTkLabel(
            header_frame,
            text="🎮 STEAM TOOLS",
            font=ctk.CTkFont(size=36, weight="bold"),
            text_color=self.colors['accent_blue']
        )
        title_label.pack()
        
        # Subtitle
        subtitle_label = ctk.CTkLabel(
            header_frame,
            text="License Activation",
            font=ctk.CTkFont(size=16),
            text_color=self.colors['text_secondary']
        )
        subtitle_label.pack(pady=(5, 0))
        
    def create_main_card(self, parent):
        # Main card frame
        card_frame = ctk.CTkFrame(
            parent,
            fg_color=self.colors['bg_secondary'],
            corner_radius=10,
            border_width=1,
            border_color=self.colors['border']
        )
        card_frame.pack(fill="both", expand=True, padx=20)
        
        # Logo section
        self.create_logo_section(card_frame)
        
        # License input section
        self.create_license_section(card_frame)
        
        # Activate button
        self.create_activate_button(card_frame)
        
        # Progress section
        self.create_progress_section(card_frame)
        
        # HWID section
        self.create_hwid_section(card_frame)
        
        # Support buttons
        self.create_support_section(card_frame)
        
    def create_logo_section(self, parent):
        logo_frame = ctk.CTkFrame(
            parent,
            fg_color=self.colors['bg_tertiary'],
            corner_radius=8,
            border_width=1,
            border_color=self.colors['border']
        )
        logo_frame.pack(fill="x", padx=30, pady=(30, 20))
        
        # Logo icon (using text emoji since we can't easily embed icons)
        icon_label = ctk.CTkLabel(
            logo_frame,
            text="🎮",
            font=ctk.CTkFont(size=48),
        )
        icon_label.pack(pady=(20, 10))
        
        # Logo text
        logo_text = ctk.CTkLabel(
            logo_frame,
            text="Steam Tools",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=self.colors['accent_blue']
        )
        logo_text.pack(pady=(0, 20))
        
    def create_license_section(self, parent):
        license_frame = ctk.CTkFrame(
            parent,
            fg_color="transparent"
        )
        license_frame.pack(fill="x", padx=30, pady=(0, 20))
        
        # Label
        license_label = ctk.CTkLabel(
            license_frame,
            text="License Key",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['text_primary'],
            anchor="w"
        )
        license_label.pack(fill="x", pady=(0, 8))
        
        # Input field
        self.license_entry = ctk.CTkEntry(
            license_frame,
            textvariable=self.license_var,
            placeholder_text="XXXX-XXXX-XXXX-XXXX",
            font=ctk.CTkFont(family="Courier", size=16),
            height=50,
            justify="center",
            fg_color=self.colors['bg_tertiary'],
            border_color=self.colors['border'],
            border_width=2
        )
        self.license_entry.pack(fill="x", pady=(0, 10))
        
    def create_activate_button(self, parent):
        self.activate_btn = ctk.CTkButton(
            parent,
            text="Activate License",
            font=ctk.CTkFont(size=16, weight="bold"),
            height=50,
            fg_color=self.colors['accent_blue'],
            hover_color="#4c9eff",
            command=self.activate_license,
            state="disabled"
        )
        self.activate_btn.pack(fill="x", padx=30, pady=(0, 20))
        
    def create_progress_section(self, parent):
        progress_frame = ctk.CTkFrame(
            parent,
            fg_color="transparent"
        )
        progress_frame.pack(fill="x", padx=30, pady=(0, 20))
        
        # Progress label frame
        label_frame = ctk.CTkFrame(
            progress_frame,
            fg_color="transparent"
        )
        label_frame.pack(fill="x", pady=(0, 8))
        
        # Progress label
        progress_label = ctk.CTkLabel(
            label_frame,
            text="Activation Progress",
            font=ctk.CTkFont(size=14),
            text_color=self.colors['text_primary']
        )
        progress_label.pack(side="left")
        
        # Progress percentage
        self.progress_percent_label = ctk.CTkLabel(
            label_frame,
            text="0%",
            font=ctk.CTkFont(size=14),
            text_color=self.colors['text_primary']
        )
        self.progress_percent_label.pack(side="right")
        
        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(
            progress_frame,
            variable=self.progress_var,
            height=12,
            fg_color=self.colors['bg_tertiary'],
            progress_color=self.colors['accent_blue']
        )
        self.progress_bar.pack(fill="x")
        
    def create_hwid_section(self, parent):
        hwid_frame = ctk.CTkFrame(
            parent,
            fg_color=self.colors['bg_tertiary'],
            corner_radius=8,
            border_width=1,
            border_color=self.colors['border']
        )
        hwid_frame.pack(fill="x", padx=30, pady=(0, 20))
        
        # HWID label
        hwid_label = ctk.CTkLabel(
            hwid_frame,
            text="Hardware ID",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.colors['text_secondary'],
            anchor="w"
        )
        hwid_label.pack(fill="x", padx=20, pady=(20, 8))
        
        # HWID value (clickable)
        self.hwid_button = ctk.CTkButton(
            hwid_frame,
            text=self.hwid,
            font=ctk.CTkFont(family="Courier", size=12),
            height=40,
            fg_color=self.colors['bg_primary'],
            hover_color=self.colors['bg_secondary'],
            text_color=self.colors['text_primary'],
            command=self.copy_hwid,
            anchor="center"
        )
        self.hwid_button.pack(fill="x", padx=20, pady=(0, 15))
        
        # Status indicator
        status_frame = ctk.CTkFrame(
            hwid_frame,
            fg_color="transparent"
        )
        status_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Status dot (using colored label)
        self.status_dot = ctk.CTkLabel(
            status_frame,
            text="●",
            font=ctk.CTkFont(size=16),
            text_color=self.colors['text_secondary'],
            width=20
        )
        self.status_dot.pack(side="left")
        
        # Status text
        self.status_label = ctk.CTkLabel(
            status_frame,
            textvariable=self.status_var,
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_secondary']
        )
        self.status_label.pack(side="left", padx=(5, 0))
        
    def create_support_section(self, parent):
        support_frame = ctk.CTkFrame(
            parent,
            fg_color="transparent"
        )
        support_frame.pack(fill="x", padx=30, pady=(0, 30))
        
        # Help button
        help_btn = ctk.CTkButton(
            support_frame,
            text="❓ Help",
            font=ctk.CTkFont(size=12),
            height=35,
            width=120,
            fg_color="transparent",
            border_width=1,
            border_color=self.colors['border'],
            text_color=self.colors['text_secondary'],
            hover_color=self.colors['bg_tertiary'],
            command=self.show_help
        )
        help_btn.pack(side="left", padx=(0, 10))
        
        # Support button
        support_btn = ctk.CTkButton(
            support_frame,
            text="💬 Support",
            font=ctk.CTkFont(size=12),
            height=35,
            width=120,
            fg_color="transparent",
            border_width=1,
            border_color=self.colors['border'],
            text_color=self.colors['text_secondary'],
            hover_color=self.colors['bg_tertiary'],
            command=self.show_support
        )
        support_btn.pack(side="left")
        
    def on_license_change(self, *args):
        """Handle license key input formatting and validation"""
        current_value = self.license_var.get()
        
        # Remove non-alphanumeric characters and convert to uppercase
        cleaned = re.sub(r'[^A-Za-z0-9]', '', current_value).upper()
        
        # Format with dashes
        formatted = ''
        for i, char in enumerate(cleaned):
            if i > 0 and i % 4 == 0 and i < 16:
                formatted += '-'
            formatted += char
            if len(formatted) >= 19:  # XXXX-XXXX-XXXX-XXXX
                break
        
        # Update the entry if formatting changed
        if formatted != current_value:
            self.license_var.set(formatted)
            return
        
        # Validate format
        is_valid = re.match(r'^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$', formatted)
        
        # Update entry appearance and button state
        if is_valid:
            self.license_entry.configure(border_color=self.colors['accent_green'])
            self.activate_btn.configure(state="normal")
        elif len(formatted) == 19:
            self.license_entry.configure(border_color=self.colors['accent_red'])
            self.activate_btn.configure(state="disabled")
        else:
            self.license_entry.configure(border_color=self.colors['border'])
            self.activate_btn.configure(state="disabled")
    
    def activate_license(self):
        """Start the license activation process"""
        if self.activate_btn.cget("state") == "disabled":
            return
            
        # Disable button and start activation
        self.activate_btn.configure(state="disabled", text="Activating...")
        self.update_status("validating", "Validating license...")
        
        # Start activation in separate thread
        threading.Thread(target=self.simulate_activation, daemon=True).start()
    
    def simulate_activation(self):
        """Simulate the activation process with progress updates"""
        steps = [
            (20, "Connecting to server..."),
            (40, "Verifying license key..."),
            (60, "Checking hardware ID..."),
            (80, "Activating license..."),
            (100, "License activated successfully!")
        ]
        
        for i, (progress, status) in enumerate(steps):
            time.sleep(1)  # Simulate processing time
            
            # Update UI in main thread
            self.root.after(0, self.update_progress, progress)
            
            if i == len(steps) - 1:
                self.root.after(0, self.update_status, "success", status)
                self.root.after(0, self.activation_complete)
            else:
                self.root.after(0, self.update_status, "validating", status)
    
    def update_progress(self, percent):
        """Update progress bar and percentage label"""
        self.progress_var.set(percent / 100.0)
        self.progress_percent_label.configure(text=f"{percent}%")
    
    def update_status(self, status_type, message):
        """Update status indicator and message"""
        color_map = {
            "ready": self.colors['text_secondary'],
            "validating": self.colors['accent_blue'],
            "success": self.colors['accent_green'],
            "error": self.colors['accent_red']
        }
        
        self.status_dot.configure(text_color=color_map.get(status_type, self.colors['text_secondary']))
        self.status_var.set(message)
    
    def activation_complete(self):
        """Handle successful activation completion"""
        self.activate_btn.configure(
            text="✓ Activated",
            fg_color=self.colors['accent_green'],
            hover_color=self.colors['accent_green'],
            state="disabled"
        )
        self.show_success_message("License activated successfully!")
    
    def copy_hwid(self):
        """Copy Hardware ID to clipboard"""
        try:
            pyperclip.copy(self.hwid)
            self.show_info_message("Hardware ID copied to clipboard!")
        except:
            # Fallback if pyperclip is not available
            self.root.clipboard_clear()
            self.root.clipboard_append(self.hwid)
            self.show_info_message("Hardware ID copied to clipboard!")
    
    def show_help(self):
        """Show help dialog"""
        messagebox.showinfo(
            "Help",
            "Steam Tools License Activation Help:\n\n"
            "1. Enter your 16-character license key\n"
            "2. Click 'Activate License' to begin\n"
            "3. Wait for the activation process to complete\n\n"
            "Format: XXXX-XXXX-XXXX-XXXX\n"
            "Only letters and numbers are allowed."
        )
    
    def show_support(self):
        """Show support dialog"""
        messagebox.showinfo(
            "Support",
            "Need help? Contact our support team:\n\n"
            "Email: <EMAIL>\n"
            "Discord: SteamTools#1234\n"
            "Website: www.steamtools.com\n\n"
            "Please include your Hardware ID when contacting support."
        )
    
    def show_success_message(self, message):
        """Show success message"""
        messagebox.showinfo("Success", message)
    
    def show_info_message(self, message):
        """Show info message"""
        messagebox.showinfo("Info", message)
    
    def run(self):
        """Start the application"""
        self.root.mainloop()

def main():
    """Main function to run the application"""
    try:
        app = SteamLicenseActivator()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        messagebox.showerror("Error", f"Failed to start application:\n{e}")

if __name__ == "__main__":
    main()