import sys
import os
import json
import time
import threading
import hashlib
import winreg
import requests
import traceback
import platform
import uuid
import psutil
import ctypes
from datetime import datetime
from pathlib import Path

from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                           QWidget, QLabel, QPushButton, QLineEdit, QTextEdit, 
                           QProgressBar, QFrame, QScrollArea, QGridLayout,
                           QMessageBox, QFileDialog, QInputDialog, QSplashScreen)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect, QSize, QPoint
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor, QLinearGradient, QPen, QBrush, QIcon, QPalette

# Import PyQt-Fluent-Widgets for modern UI
try:
    from qfluentwidgets import (
        FluentIcon, PushButton, PrimaryPushButton, InfoBar, InfoBarPosition,
        CardWidget, StrongBodyLabel, BodyLabel, CaptionLabel, SubtitleLabel,
        LineEdit, ProgressBar, ProgressRing, InfoBadge, Pivot, PivotItem,
        ScrollArea, SmoothScrollArea, VBoxLayout, HBoxLayout, FlowLayout,
        Theme, setTheme, isDarkTheme, qconfig, ConfigItem, RangeConfigItem,
        ComboBoxSettingCard, SwitchSettingCard, PushSettingCard, HyperlinkCard,
        ElevatedCardWidget, SimpleCardWidget, HeaderCardWidget,
        NavigationInterface, NavigationItemPosition, NavigationWidget,
        FluentWindow, SplitFluentWindow, FluentBackgroundTheme,
        MSFluentWindow, MSFluentTitleBar, setThemeColor,
        StateToolTip, TeachingTip, TeachingTipTailPosition,
        PipsPager, SegmentedWidget, Flyout, FlyoutAnimationType,
        MessageBox, Dialog, ColorDialog, FolderListDialog,
        ThemeColor, setCustomStyleSheet
    )
    FLUENT_WIDGETS_AVAILABLE = True
except ImportError:
    FLUENT_WIDGETS_AVAILABLE = False
    print("PyQt-Fluent-Widgets not available. Please install with: pip install 'PyQt-Fluent-Widgets[full]'")

# Add parent directory to path to import keyauth
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
try:
    from keyauth import api
except ImportError:
    print("Warning: keyauth module not found")
    api = None

class SteamTheme:
    """Steam-inspired color theme with modern design"""
    # Steam primary colors
    BACKGROUND = QColor(14, 20, 25)  # #0e1419
    SURFACE = QColor(27, 40, 56)     # #1b2838
    SURFACE_VARIANT = QColor(23, 26, 33)  # #171a21
    PRIMARY = QColor(102, 192, 244)   # #66c0f4
    PRIMARY_VARIANT = QColor(76, 159, 239)  # #4c9fff
    SECONDARY = QColor(42, 71, 94)    # #2a475e
    SUCCESS = QColor(76, 107, 34)     # #4c6b22
    WARNING = QColor(255, 184, 0)     # #ffb800
    ERROR = QColor(205, 65, 43)       # #cd412b
    
    # Text colors
    ON_BACKGROUND = QColor(199, 213, 224)  # #c7d5e0
    ON_SURFACE = QColor(199, 213, 224)     # #c7d5e0
    ON_PRIMARY = QColor(14, 20, 25)        # #0e1419
    MUTED = QColor(139, 152, 165)          # #8b98a5

class ConnectionStatusWidget(QWidget):
    """Modern connection status indicator with animations"""
    
    def __init__(self):
        super().__init__()
        self.status = "connecting"
        self.init_ui()
        self.setup_animations()
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(16, 12, 16, 12)
        
        # Status indicator
        self.indicator = QLabel("⏳")
        self.indicator.setFixedSize(24, 24)
        self.indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.indicator.setStyleSheet(f"""
            QLabel {{
                background-color: {SteamTheme.SURFACE.name()};
                border-radius: 12px;
                font-size: 14px;
            }}
        """)
        
        # Status text
        self.status_label = QLabel("Connecting to License Server")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.ON_SURFACE.name()};
                font-weight: bold;
                font-size: 13px;
            }}
        """)
        
        # Details text
        self.details_label = QLabel("Initializing connection...")
        self.details_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.MUTED.name()};
                font-size: 11px;
            }}
        """)
        
        # Layout
        text_layout = QVBoxLayout()
        text_layout.setSpacing(2)
        text_layout.addWidget(self.status_label)
        text_layout.addWidget(self.details_label)
        
        layout.addWidget(self.indicator)
        layout.addLayout(text_layout)
        layout.addStretch()
        
        # Metrics
        metrics_layout = QVBoxLayout()
        self.latency_label = QLabel("24ms")
        self.uptime_label = QLabel("99.9%")
        
        for label in [self.latency_label, self.uptime_label]:
            label.setStyleSheet(f"""
                QLabel {{
                    color: {SteamTheme.SUCCESS.name()};
                    font-family: 'Consolas', monospace;
                    font-size: 11px;
                    font-weight: bold;
                }}
            """)
        
        metrics_layout.addWidget(self.latency_label)
        metrics_layout.addWidget(self.uptime_label)
        layout.addLayout(metrics_layout)
        
        self.setStyleSheet(f"""
            ConnectionStatusWidget {{
                background-color: {SteamTheme.SURFACE.name()};
                border: 1px solid {SteamTheme.SECONDARY.name()};
                border-radius: 8px;
            }}
        """)
        
    def setup_animations(self):
        self.pulse_timer = QTimer()
        self.pulse_timer.timeout.connect(self.pulse_animation)
        self.pulse_timer.start(2000)
        
    def pulse_animation(self):
        if self.status == "connecting":
            # Animate connecting indicator
            self.indicator.setText("⏳" if self.indicator.text() == "⏳" else "🔄")
        
    def update_status(self, status, message=None, details=None):
        self.status = status
        
        status_config = {
            "connecting": {
                "icon": "⏳",
                "color": SteamTheme.PRIMARY.name(),
                "message": "Connecting to License Server"
            },
            "connected": {
                "icon": "✅",
                "color": SteamTheme.SUCCESS.name(),
                "message": "Connected to License Server"
            },
            "disconnected": {
                "icon": "❌",
                "color": SteamTheme.ERROR.name(),
                "message": "Disconnected from Server"
            },
            "error": {
                "icon": "⚠️",
                "color": SteamTheme.WARNING.name(),
                "message": "Connection Error"
            }
        }
        
        config = status_config.get(status, status_config["connecting"])
        
        self.indicator.setText(config["icon"])
        self.status_label.setText(message or config["message"])
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {config["color"]};
                font-weight: bold;
                font-size: 13px;
            }}
        """)
        
        if details:
            self.details_label.setText(details)

class ProgressWidget(QWidget):
    """Modern progress widget with animations"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        
        # Progress header
        header_layout = QHBoxLayout()
        self.progress_label = QLabel("Activation Progress")
        self.progress_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.ON_SURFACE.name()};
                font-size: 13px;
                font-weight: bold;
            }}
        """)
        
        self.percentage_label = QLabel("0%")
        self.percentage_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.ON_SURFACE.name()};
                font-family: 'Consolas', monospace;
                font-size: 13px;
                font-weight: bold;
            }}
        """)
        
        header_layout.addWidget(self.progress_label)
        header_layout.addStretch()
        header_layout.addWidget(self.percentage_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(12)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: none;
                border-radius: 6px;
                background-color: {SteamTheme.SURFACE_VARIANT.name()};
            }}
            QProgressBar::chunk {{
                border-radius: 6px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {SteamTheme.PRIMARY.name()}, 
                    stop:1 {SteamTheme.PRIMARY_VARIANT.name()});
            }}
        """)
        
        # Status message
        self.status_message = QLabel("🚀 Ready to activate")
        self.status_message.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.MUTED.name()};
                font-size: 12px;
                padding: 8px 0px;
            }}
        """)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_message)
        
    def update_progress(self, value, message=None):
        self.progress_bar.setValue(value)
        self.percentage_label.setText(f"{value}%")
        if message:
            self.status_message.setText(message)

class HWIDWidget(QWidget):
    """Hardware ID display widget with copy functionality"""
    
    def __init__(self, hwid):
        super().__init__()
        self.hwid = hwid
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        
        # Header
        header_layout = QHBoxLayout()
        icon_label = QLabel("🖥️")
        icon_label.setFixedSize(20, 20)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        title_label = QLabel("Hardware ID")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.ON_SURFACE.name()};
                font-size: 13px;
                font-weight: bold;
            }}
        """)
        
        self.copy_status = QLabel("")
        self.copy_status.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.SUCCESS.name()};
                font-size: 10px;
            }}
        """)
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.copy_status)
        
        # HWID button
        self.hwid_button = QPushButton(self.hwid)
        self.hwid_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.hwid_button.clicked.connect(self.copy_hwid)
        self.hwid_button.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                border: 1px solid {SteamTheme.SECONDARY.name()};
                border-radius: 6px;
                padding: 8px 12px;
                color: {SteamTheme.PRIMARY.name()};
                font-family: 'Consolas', monospace;
                font-size: 11px;
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: {SteamTheme.SURFACE_VARIANT.name()};
                border-color: {SteamTheme.PRIMARY.name()};
            }}
        """)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.hwid_button)
        
        self.setStyleSheet(f"""
            HWIDWidget {{
                background-color: {SteamTheme.SURFACE.name()};
                border: 1px solid {SteamTheme.SECONDARY.name()};
                border-radius: 8px;
                padding: 12px;
            }}
        """)
        
    def copy_hwid(self):
        clipboard = QApplication.clipboard()
        clipboard.setText(self.hwid)
        
        self.copy_status.setText("✅ Copied!")
        QTimer.singleShot(3000, lambda: self.copy_status.setText(""))

class StatusLogWidget(QWidget):
    """Status log with modern styling"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        
        # Header
        header_label = QLabel("📋 Status Log")
        header_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.ON_SURFACE.name()};
                font-size: 13px;
                font-weight: bold;
                padding-bottom: 4px;
            }}
        """)
        
        # Log text area
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {SteamTheme.BACKGROUND.name()};
                border: 1px solid {SteamTheme.SECONDARY.name()};
                border-radius: 6px;
                color: {SteamTheme.ON_BACKGROUND.name()};
                font-family: 'Consolas', monospace;
                font-size: 10px;
                padding: 8px;
            }}
        """)
        
        layout.addWidget(header_label)
        layout.addWidget(self.log_text)
        
    def add_message(self, message):
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.append(log_entry.strip())
        
        # Auto-scroll to bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

class LicenseActivationThread(QThread):
    """Background thread for license activation"""
    
    progress_updated = pyqtSignal(int, str)
    status_message = pyqtSignal(str)
    activation_complete = pyqtSignal(bool, str)
    
    def __init__(self, license_key, keyauth_app, steam_path, app_info):
        super().__init__()
        self.license_key = license_key
        self.keyauth_app = keyauth_app
        self.steam_path = steam_path
        self.app_info = app_info
        
    def run(self):
        try:
            self.progress_updated.emit(10, "Validating license key...")
            time.sleep(0.5)
            
            # Validate license
            if not self.keyauth_app:
                self.activation_complete.emit(False, "Keyauth not initialized")
                return
                
            is_valid = self.keyauth_app.license(self.license_key)
            if not is_valid:
                self.activation_complete.emit(False, "Invalid license key")
                return
                
            self.progress_updated.emit(30, "License validated successfully")
            self.status_message.emit("✅ License key validated")
            time.sleep(0.5)
            
            self.progress_updated.emit(50, "Processing activation files...")
            time.sleep(1)
            
            # Simulate file processing
            success = self.process_activation_files()
            
            if success:
                self.progress_updated.emit(100, "Activation completed successfully!")
                self.activation_complete.emit(True, "Steam activation completed successfully!")
            else:
                self.activation_complete.emit(False, "Failed to process activation files")
                
        except Exception as e:
            self.activation_complete.emit(False, f"Activation error: {str(e)}")
            
    def process_activation_files(self):
        try:
            # Create necessary directories
            stplug_path = os.path.join(self.steam_path, "config", "stplug-in")
            os.makedirs(stplug_path, exist_ok=True)
            
            # Simulate file downloads with progress updates
            files = ["HID Library", "Lua Packer", "Steam Tools", "Game Script"]
            
            for i, file_name in enumerate(files):
                progress = 50 + (i + 1) * 10
                self.progress_updated.emit(progress, f"Processing {file_name}...")
                self.status_message.emit(f"📥 Processing {file_name}...")
                time.sleep(0.8)  # Simulate processing time
                
            return True
            
        except Exception as e:
            self.status_message.emit(f"❌ Error processing files: {str(e)}")
            return False

class ModernSteamToolsGUI(QMainWindow):
    """Modern Steam Tools GUI with PyQt-Fluent-Widgets styling"""
    
    def __init__(self):
        super().__init__()
        self.keyauth_app = None
        self.steam_path = ""
        self.license_key = ""
        self.hwid = self.generate_hwid()
        
        # Setup theme and styling
        self.setup_theme()
        self.init_ui()
        self.init_keyauth()
        self.auto_detect_steam_path()
        
    def setup_theme(self):
        """Setup Steam-inspired dark theme"""
        if FLUENT_WIDGETS_AVAILABLE:
            setTheme(Theme.DARK)
            setThemeColor(SteamTheme.PRIMARY)
        
        # Apply custom dark palette
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {SteamTheme.BACKGROUND.name()};
                color: {SteamTheme.ON_BACKGROUND.name()};
            }}
        """)
        
    def init_ui(self):
        """Initialize the modern user interface"""
        self.setWindowTitle("🎮 Steam Tools - License Activation")
        self.setFixedSize(800, 700)
        self.setWindowIcon(self.create_app_icon())
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Header
        self.create_header(main_layout)
        
        # Main card
        self.create_main_card(main_layout)
        
        # Status bar
        self.create_status_bar()
        
    def create_app_icon(self):
        """Create application icon"""
        pixmap = QPixmap(32, 32)
        pixmap.fill(Qt.GlobalColor.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Draw Steam-like icon
        gradient = QLinearGradient(0, 0, 32, 32)
        gradient.setColorAt(0, SteamTheme.PRIMARY)
        gradient.setColorAt(1, SteamTheme.PRIMARY_VARIANT)
        
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(0, 0, 32, 32, 8, 8)
        
        painter.setPen(QPen(SteamTheme.ON_PRIMARY, 2))
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "🎮")
        painter.end()
        
        return QIcon(pixmap)
        
    def create_header(self, layout):
        """Create header section"""
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)
        header_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.setSpacing(8)
        
        # Title
        title_label = QLabel("🎮 STEAM TOOLS")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.PRIMARY.name()};
                font-size: 32px;
                font-weight: bold;
                padding: 0px;
            }}
        """)
        
        # Subtitle
        subtitle_label = QLabel("License Activation")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.MUTED.name()};
                font-size: 16px;
                padding: 0px;
            }}
        """)
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        layout.addWidget(header_widget)
        
    def create_main_card(self, layout):
        """Create main activation card"""
        # Main card container
        card_widget = QWidget()
        card_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {SteamTheme.SURFACE.name()};
                border: 1px solid {SteamTheme.SECONDARY.name()};
                border-radius: 12px;
            }}
        """)
        
        card_layout = QVBoxLayout(card_widget)
        card_layout.setSpacing(20)
        card_layout.setContentsMargins(24, 24, 24, 24)
        
        # Logo section
        self.create_logo_section(card_layout)
        
        # License input section
        self.create_license_section(card_layout)
        
        # Activate button
        self.create_activate_button(card_layout)
        
        # Progress section
        self.progress_widget = ProgressWidget()
        card_layout.addWidget(self.progress_widget)
        
        # Bottom sections
        self.create_bottom_sections(card_layout)
        
        layout.addWidget(card_widget)
        
    def create_logo_section(self, layout):
        """Create logo section"""
        logo_widget = QWidget()
        logo_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {SteamTheme.SURFACE_VARIANT.name()};
                border: 1px solid {SteamTheme.SECONDARY.name()};
                border-radius: 8px;
            }}
        """)
        
        logo_layout = QVBoxLayout(logo_widget)
        logo_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        logo_layout.setSpacing(12)
        logo_layout.setContentsMargins(20, 20, 20, 20)
        
        # Icon
        icon_label = QLabel("🎮")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("font-size: 48px;")
        
        # Text
        text_label = QLabel("Steam Tools")
        text_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        text_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.PRIMARY.name()};
                font-size: 24px;
                font-weight: bold;
            }}
        """)
        
        logo_layout.addWidget(icon_label)
        logo_layout.addWidget(text_label)
        layout.addWidget(logo_widget)
        
    def create_license_section(self, layout):
        """Create license input section"""
        # Label
        license_label = QLabel("License Key")
        license_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.ON_SURFACE.name()};
                font-size: 14px;
                font-weight: bold;
                padding-bottom: 8px;
            }}
        """)
        
        # Input
        self.license_input = QLineEdit()
        self.license_input.setPlaceholderText("Enter your license key here...")
        self.license_input.setFixedHeight(40)
        self.license_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.license_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {SteamTheme.SURFACE_VARIANT.name()};
                border: 2px solid {SteamTheme.SECONDARY.name()};
                border-radius: 8px;
                padding: 0px 16px;
                color: {SteamTheme.ON_SURFACE.name()};
                font-size: 16px;
                font-family: 'Consolas', monospace;
            }}
            QLineEdit:focus {{
                border-color: {SteamTheme.PRIMARY.name()};
            }}
        """)
        
        # Bind Enter key
        self.license_input.returnPressed.connect(self.activate_license)
        
        layout.addWidget(license_label)
        layout.addWidget(self.license_input)
        
    def create_activate_button(self, layout):
        """Create activate button"""
        self.activate_button = QPushButton("🚀 Activate License")
        self.activate_button.setFixedHeight(50)
        self.activate_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.activate_button.clicked.connect(self.activate_license)
        self.activate_button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {SteamTheme.PRIMARY.name()}, 
                    stop:1 {SteamTheme.PRIMARY_VARIANT.name()});
                border: none;
                border-radius: 8px;
                color: {SteamTheme.ON_PRIMARY.name()};
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {SteamTheme.PRIMARY_VARIANT.name()}, 
                    stop:1 {SteamTheme.PRIMARY.name()});
            }}
            QPushButton:pressed {{
                background-color: {SteamTheme.SECONDARY.name()};
            }}
            QPushButton:disabled {{
                background-color: {SteamTheme.SECONDARY.name()};
                color: {SteamTheme.MUTED.name()};
            }}
        """)
        
        layout.addWidget(self.activate_button)
        
    def create_bottom_sections(self, layout):
        """Create bottom information sections"""
        # Connection status and HWID row
        info_row = QHBoxLayout()
        info_row.setSpacing(16)
        
        # Connection status
        self.connection_widget = ConnectionStatusWidget()
        info_row.addWidget(self.connection_widget, 1)
        
        # HWID widget
        hwid_widget = HWIDWidget(self.hwid)
        info_row.addWidget(hwid_widget, 1)
        
        layout.addLayout(info_row)
        
        # Status log
        self.status_log = StatusLogWidget()
        layout.addWidget(self.status_log)
        
        # Support buttons
        self.create_support_buttons(layout)
        
    def create_support_buttons(self, layout):
        """Create support buttons"""
        support_layout = QVBoxLayout()
        support_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        support_layout.setSpacing(8)
        
        # Hint
        hint_label = QLabel("💡 Press Ctrl+Shift+A for admin mode")
        hint_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        hint_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.MUTED.name()};
                font-size: 11px;
            }}
        """)
        
        # Buttons row
        buttons_layout = QHBoxLayout()
        buttons_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        buttons_layout.setSpacing(16)
        
        help_btn = QPushButton("❓ Help")
        support_btn = QPushButton("💬 Support")
        
        for btn in [help_btn, support_btn]:
            btn.setFixedSize(120, 35)
            btn.setCursor(Qt.CursorShape.PointingHandCursor)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: transparent;
                    border: 1px solid {SteamTheme.SECONDARY.name()};
                    border-radius: 6px;
                    color: {SteamTheme.MUTED.name()};
                    font-size: 12px;
                }}
                QPushButton:hover {{
                    border-color: {SteamTheme.PRIMARY.name()};
                    color: {SteamTheme.PRIMARY.name()};
                }}
            """)
            
        help_btn.clicked.connect(self.show_help)
        support_btn.clicked.connect(self.show_support)
        
        buttons_layout.addWidget(help_btn)
        buttons_layout.addWidget(support_btn)
        
        support_layout.addWidget(hint_label)
        support_layout.addLayout(buttons_layout)
        layout.addLayout(support_layout)
        
    def create_status_bar(self):
        """Create status bar"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet(f"""
            QStatusBar {{
                background-color: {SteamTheme.SURFACE.name()};
                border-top: 1px solid {SteamTheme.SECONDARY.name()};
                color: {SteamTheme.ON_SURFACE.name()};
                font-size: 11px;
                padding: 8px;
            }}
        """)
        
        # Admin status
        admin_status = "🛡️ Admin: Yes" if self.is_admin() else "⚠️ Admin: No"
        steam_status = f"🎮 Steam: {self.steam_path}" if self.steam_path else "❌ Steam: Not Found"
        
        status_bar.showMessage(f"{admin_status} • {steam_status}")
        
    def generate_hwid(self):
        """Generate Hardware ID"""
        try:
            system_info = []
            
            # MAC Address
            try:
                import uuid
                mac = uuid.getnode()
                system_info.append(f"MAC:{mac}")
            except:
                pass
                
            # System info
            try:
                system_info.append(f"SYSTEM:{platform.system()}")
                system_info.append(f"MACHINE:{platform.machine()}")
                system_info.append(f"NODE:{platform.node()}")
            except:
                pass
                
            if system_info:
                combined_info = "|".join(sorted(system_info))
                hwid_hash = hashlib.sha256(combined_info.encode()).hexdigest()
                return "-".join([hwid_hash[i:i+4].upper() for i in range(0, 16, 4)])
            else:
                return str(uuid.uuid4()).replace("-", "")[:16].upper()
                
        except Exception:
            return "HWID-" + str(uuid.uuid4()).replace("-", "")[:12].upper()
            
    def is_admin(self):
        """Check if running as administrator"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
            
    def auto_detect_steam_path(self):
        """Auto-detect Steam installation path"""
        try:
            # Try registry lookup
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Valve\Steam") as key:
                steam_path = winreg.QueryValueEx(key, "InstallPath")[0]
                if os.path.exists(steam_path):
                    self.steam_path = steam_path
                    self.status_log.add_message(f"Steam path detected: {steam_path}")
                    return
        except:
            pass
            
        # Try common paths
        common_paths = [
            r"C:\Program Files (x86)\Steam",
            r"C:\Program Files\Steam",
            r"D:\Steam",
            r"E:\Steam"
        ]
        
        for path in common_paths:
            if os.path.exists(path) and os.path.exists(os.path.join(path, "steam.exe")):
                self.steam_path = path
                self.status_log.add_message(f"Steam path found: {path}")
                return
                
        self.status_log.add_message("Steam installation not found automatically")
        
    def init_keyauth(self):
        """Initialize Keyauth API"""
        def init_in_background():
            try:
                self.connection_widget.update_status("connecting", "Connecting to license server...", "Initializing Keyauth API...")
                
                checksum = self.get_checksum()
                self.keyauth_app = api(
                    name="MainSteam",
                    ownerid="1tGVnUKtzH",
                    secret="eb95192c2d44019fc97805ceb1986dcc70f9c54ccffa1cebce98973ab74a669f",
                    version="1.0",
                    hash_to_check=checksum
                )
                
                # Update UI on main thread
                QTimer.singleShot(100, lambda: self.connection_widget.update_status(
                    "connected", 
                    "Connected to license server",
                    "Connection established successfully"
                ))
                QTimer.singleShot(100, lambda: self.status_log.add_message("✅ Keyauth initialized successfully"))
                
            except Exception as e:
                QTimer.singleShot(100, lambda: self.connection_widget.update_status(
                    "error",
                    "License server error", 
                    "Connection failed"
                ))
                QTimer.singleShot(100, lambda: self.status_log.add_message("❌ Failed to initialize connection"))
                self.keyauth_app = None
                
        threading.Thread(target=init_in_background, daemon=True).start()
        
    def get_checksum(self):
        """Get file checksum for Keyauth"""
        try:
            md5_hash = hashlib.md5()
            with open(__file__, "rb") as f:
                md5_hash.update(f.read())
            return md5_hash.hexdigest()
        except:
            return ""
            
    def activate_license(self):
        """Activate license key"""
        license_key = self.license_input.text().strip()
        
        if not license_key:
            QMessageBox.warning(self, "Error", "Please enter a license key")
            return
            
        if not self.keyauth_app:
            QMessageBox.warning(self, "Error", "Not connected to license server. Please wait and try again.")
            return
            
        if not self.steam_path:
            self.auto_detect_steam_path()
            if not self.steam_path:
                QMessageBox.critical(self, "Error", "Steam installation not found. Please contact administrator.")
                return
                
        # Check if Steam is running
        if self.is_steam_running():
            reply = QMessageBox.question(
                self, 
                "Steam Running", 
                "Steam is currently running and must be closed before activation. Close Steam now?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.close_steam_processes()
            else:
                return
                
        # Start activation in background thread
        self.activate_button.setEnabled(False)
        self.activate_button.setText("⏳ Activating...")
        self.license_input.setEnabled(False)
        
        # Get app info (simplified for this example)
        app_info = {"app_id": "123456", "app_name": "Steam Game"}
        
        self.activation_thread = LicenseActivationThread(
            license_key, self.keyauth_app, self.steam_path, app_info
        )
        self.activation_thread.progress_updated.connect(self.progress_widget.update_progress)
        self.activation_thread.status_message.connect(self.status_log.add_message)
        self.activation_thread.activation_complete.connect(self.on_activation_complete)
        self.activation_thread.start()
        
    def on_activation_complete(self, success, message):
        """Handle activation completion"""
        self.activate_button.setEnabled(True)
        self.license_input.setEnabled(True)
        
        if success:
            self.activate_button.setText("✅ Activated")
            self.activate_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {SteamTheme.SUCCESS.name()};
                    border: none;
                    border-radius: 8px;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                }}
            """)
            QMessageBox.information(self, "Success", message)
        else:
            self.activate_button.setText("❌ Failed")
            self.activate_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {SteamTheme.ERROR.name()};
                    border: none;
                    border-radius: 8px;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                }}
            """)
            QMessageBox.critical(self, "Error", message)
            
        # Reset button after delay
        QTimer.singleShot(3000, self.reset_activate_button)
        
    def reset_activate_button(self):
        """Reset activate button to original state"""
        self.activate_button.setText("🚀 Activate License")
        self.activate_button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {SteamTheme.PRIMARY.name()}, 
                    stop:1 {SteamTheme.PRIMARY_VARIANT.name()});
                border: none;
                border-radius: 8px;
                color: {SteamTheme.ON_PRIMARY.name()};
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {SteamTheme.PRIMARY_VARIANT.name()}, 
                    stop:1 {SteamTheme.PRIMARY.name()});
            }}
        """)
        
    def is_steam_running(self):
        """Check if Steam is running"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and proc.info['name'].lower() in ['steam.exe', 'steamwebhelper.exe', 'steamservice.exe']:
                    return True
            return False
        except Exception:
            return False
            
    def close_steam_processes(self):
        """Close Steam processes"""
        try:
            steam_processes = []
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and proc.info['name'].lower() in ['steam.exe', 'steamwebhelper.exe', 'steamservice.exe']:
                    steam_processes.append(proc)
                    
            for proc in steam_processes:
                try:
                    proc.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
                    
            time.sleep(2)
            
            for proc in steam_processes:
                try:
                    if proc.is_running():
                        proc.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
                    
            self.status_log.add_message(f"✅ Closed {len(steam_processes)} Steam process(es)")
            
        except Exception as e:
            self.status_log.add_message(f"❌ Error closing Steam: {str(e)}")
            
    def show_help(self):
        """Show help dialog"""
        QMessageBox.information(
            self,
            "Help",
            "Steam Tools License Activation Help:\n\n"
            "1. Enter your license key\n"
            "2. Click 'Activate License' to begin\n"
            "3. Wait for the activation process to complete\n\n"
            "For support, contact your administrator."
        )
        
    def show_support(self):
        """Show support dialog"""
        QMessageBox.information(
            self,
            "Support",
            "Need help? Contact support:\n\n"
            "Please contact your administrator for assistance.\n"
            "Include any error messages when requesting help."
        )

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setApplicationName("Steam Tools")
    app.setApplicationVersion("2.0")
    
    # Create and show splash screen
    splash_pixmap = QPixmap(400, 200)
    splash_pixmap.fill(SteamTheme.BACKGROUND)
    
    painter = QPainter(splash_pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # Draw splash content
    painter.setPen(QPen(SteamTheme.PRIMARY, 2))
    painter.setFont(QFont("Arial", 24, QFont.Weight.Bold))
    painter.drawText(splash_pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "🎮 Steam Tools v2.0\nLoading...")
    painter.end()
    
    splash = QSplashScreen(splash_pixmap)
    splash.show()
    app.processEvents()
    
    # Create main window
    window = ModernSteamToolsGUI()
    
    # Show main window and hide splash
    QTimer.singleShot(2000, lambda: (splash.finish(window), window.show()))
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()