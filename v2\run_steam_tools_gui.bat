@echo off
title Steam Tools Downloader v2

:: Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator - OK
    echo Script directory: %SCRIPT_DIR%
    echo Starting Steam Tools Downloader v2...

    :: Change to the script directory and run Python
    cd /d "%SCRIPT_DIR%"
    python steam_tools_gui.py
) else (
    echo Requesting Administrator privileges...
    echo This application requires administrator privileges to write files to Steam folder.
    echo.
    echo Please wait for UAC prompt...

    :: Create a temporary script that changes directory and runs the original batch
    echo @echo off > "%TEMP%\run_steam_admin.bat"
    echo cd /d "%SCRIPT_DIR%" >> "%TEMP%\run_steam_admin.bat"
    echo call "%~f0" >> "%TEMP%\run_steam_admin.bat"

    :: Run the temporary script as administrator
    powershell -Command "Start-Process '%TEMP%\run_steam_admin.bat' -Verb RunAs"
    exit /b
)

pause
