@echo off
title Steam Tools Downloader v2 - Build Script
color 0A

echo ========================================
echo Steam Tools Downloader v2 - Build
echo ========================================
echo.
echo This script will create a single executable file
echo that can be distributed without Python installation.
echo.
echo Requirements:
echo - Python 3.7+ installed
echo - Internet connection for downloading dependencies
echo.
pause

echo.
echo Starting build process...
echo.

python build_exe.py

if errorlevel 1 (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo Please check the error messages above.
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo The executable has been created and packaged.
    echo Check the SteamToolsDownloader_v2_Distribution folder.
    echo.
    pause
)
