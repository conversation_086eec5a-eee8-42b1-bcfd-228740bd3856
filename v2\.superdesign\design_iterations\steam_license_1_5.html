<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steam Tools - License Activation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <style>
        /* Import Steam Theme */
        :root {
            --background: #0e1419;
            --foreground: #c7d5e0;
            --card: #1b2838;
            --card-foreground: #c7d5e0;
            --popover: #1b2838;
            --popover-foreground: #c7d5e0;
            --primary: #66c0f4;
            --primary-foreground: #0e1419;
            --secondary: #2a475e;
            --secondary-foreground: #c7d5e0;
            --muted: #171a21;
            --muted-foreground: #8b98a5;
            --accent: #4c6b22;
            --accent-foreground: #c7d5e0;
            --destructive: #cd412b;
            --destructive-foreground: #ffffff;
            --border: #2a475e;
            --input: #1b2838;
            --ring: #66c0f4;
            --success: #00d26a;
            --warning: #ffb800;
            --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
            --radius: 0.375rem;
            --steam-gradient-primary: linear-gradient(135deg, #66c0f4 0%, #4c9eff 100%);
            --steam-gradient-secondary: linear-gradient(135deg, #2a475e 0%, #1b2838 100%);
            --steam-gradient-success: linear-gradient(135deg, #4c6b22 0%, #5c7e10 100%);
            --steam-gradient-card: linear-gradient(145deg, #1b2838 0%, #16202d 100%);
            --steam-glow: 0 0 20px rgba(102, 192, 244, 0.3);
            --success-glow: 0 0 15px rgba(0, 210, 106, 0.4);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-sans) !important;
            background: var(--background) !important;
            color: var(--foreground) !important;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Steam-inspired animations */
        @keyframes steamGlow {
            0%, 100% { box-shadow: var(--steam-glow); }
            50% { box-shadow: 0 0 30px rgba(102, 192, 244, 0.5); }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes progressFill {
            from { width: 0%; }
            to { width: var(--progress-width, 0%); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes connectionPulse {
            0%, 100% { 
                box-shadow: 0 0 0 0 rgba(0, 210, 106, 0.7);
                transform: scale(1);
            }
            50% { 
                box-shadow: 0 0 0 8px rgba(0, 210, 106, 0);
                transform: scale(1.05);
            }
        }

        @keyframes dataFlow {
            0% { transform: translateX(-100%) scaleX(0); }
            50% { transform: translateX(0%) scaleX(1); }
            100% { transform: translateX(100%) scaleX(0); }
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Main container */
        .steam-container {
            background: var(--background);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
        }

        .steam-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 50%, rgba(102, 192, 244, 0.05) 0%, transparent 70%);
            pointer-events: none;
        }

        /* Connection Status Bar */
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--steam-gradient-card);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            z-index: 1000;
            animation: slideInUp 1s ease-out 0.5s both;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .connection-indicator {
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .connection-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--success);
            animation: connectionPulse 2s infinite;
            box-shadow: var(--success-glow);
        }

        .connection-dot.connecting {
            background: var(--warning);
            animation: pulse 1s infinite;
            box-shadow: 0 0 15px rgba(255, 184, 0, 0.4);
        }

        .connection-dot.disconnected {
            background: var(--destructive);
            animation: none;
            box-shadow: 0 0 15px rgba(205, 65, 43, 0.4);
        }

        .connection-text {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--foreground);
        }

        .connection-details {
            font-size: 0.75rem;
            color: var(--muted-foreground);
            margin-left: 4px;
        }

        /* Server Status Panel */
        .server-status-panel {
            background: var(--steam-gradient-secondary);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1rem;
            margin-bottom: 1.5rem;
            animation: fadeInScale 0.6s ease-out 0.3s both;
        }

        .server-status-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.75rem;
        }

        .server-status-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--foreground);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .server-icon {
            width: 16px;
            height: 16px;
            color: var(--primary);
        }

        .server-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 0.75rem;
        }

        .metric-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .metric-label {
            font-size: 0.75rem;
            color: var(--muted-foreground);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .metric-value {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--success);
            font-family: var(--font-mono);
        }

        .data-flow-indicator {
            height: 2px;
            background: var(--muted);
            border-radius: 1px;
            overflow: hidden;
            position: relative;
            margin-top: 0.5rem;
        }

        .data-flow-bar {
            height: 100%;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
            animation: dataFlow 2s infinite;
            border-radius: 1px;
        }

        /* Header */
        .steam-header {
            text-align: center;
            margin-bottom: 3rem;
            animation: slideInUp 0.8s ease-out;
        }

        .steam-title {
            font-size: 2.5rem !important;
            font-weight: 700 !important;
            color: var(--primary) !important;
            margin-bottom: 0.5rem;
            text-shadow: 0 0 20px rgba(102, 192, 244, 0.3);
        }

        .steam-subtitle {
            font-size: 1.125rem !important;
            color: var(--muted-foreground) !important;
            font-weight: 400 !important;
        }

        /* Main card */
        .steam-card {
            background: var(--steam-gradient-card);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 3rem;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 20px 25px 0 rgba(0, 0, 0, 0.5), 0 10px 10px 0 rgba(0, 0, 0, 0.4);
            animation: slideInUp 0.8s ease-out 0.2s both;
            position: relative;
        }

        .steam-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
            opacity: 0.5;
        }

        /* Logo area */
        .steam-logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: var(--steam-gradient-secondary);
            border-radius: var(--radius);
            border: 1px solid var(--border);
            gap: 1rem;
        }

        .steam-logo-icon {
            width: 64px !important;
            height: 64px !important;
            color: var(--primary) !important;
            filter: drop-shadow(var(--steam-glow));
            flex-shrink: 0;
            display: block;
        }

        .steam-logo-text {
            font-size: 1.5rem !important;
            font-weight: 600 !important;
            color: var(--primary) !important;
            margin: 0 !important;
            line-height: 1.2 !important;
            display: block;
        }

        /* License input */
        .license-section {
            margin-bottom: 2rem;
        }

        .license-label {
            display: block;
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            color: var(--foreground) !important;
            margin-bottom: 0.5rem;
        }

        .license-input {
            width: 100%;
            padding: 1rem;
            background: var(--input) !important;
            border: 2px solid var(--border) !important;
            border-radius: var(--radius) !important;
            color: var(--foreground) !important;
            font-family: var(--font-mono) !important;
            font-size: 1.125rem !important;
            text-align: center;
            letter-spacing: 0.1em;
            transition: all 0.3s ease;
        }

        .license-input:focus {
            outline: none !important;
            border-color: var(--primary) !important;
            box-shadow: var(--steam-glow), 0 0 0 3px rgba(102, 192, 244, 0.1) !important;
            transform: scale(1.02);
        }

        .license-input.error {
            border-color: var(--destructive) !important;
            animation: shake 0.4s ease-in-out;
        }

        .license-input.success {
            border-color: var(--accent) !important;
            box-shadow: 0 0 20px rgba(76, 107, 34, 0.3);
        }

        /* Activate button */
        .activate-btn {
            width: 100%;
            padding: 1rem 2rem;
            background: var(--steam-gradient-primary) !important;
            border: none !important;
            border-radius: var(--radius) !important;
            color: var(--primary-foreground) !important;
            font-size: 1.125rem !important;
            font-weight: 600 !important;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .activate-btn:hover {
            transform: scale(1.05);
            box-shadow: var(--steam-glow);
        }

        .activate-btn:active {
            transform: scale(0.98);
        }

        .activate-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .activate-btn.loading {
            color: transparent;
        }

        .activate-btn .spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid var(--primary-foreground);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            opacity: 0;
        }

        .activate-btn.loading .spinner {
            opacity: 1;
        }

        .btn-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            width: 100%;
        }

        .btn-icon {
            width: 20px !important;
            height: 20px !important;
            flex-shrink: 0;
        }

        .btn-text {
            line-height: 1;
        }

        /* Progress bar */
        .progress-section {
            margin-bottom: 2rem;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.875rem !important;
            color: var(--foreground) !important;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--muted);
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: var(--steam-gradient-primary);
            border-radius: 4px;
            transition: width 0.8s ease-out;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* HWID section */
        .hwid-section {
            background: var(--steam-gradient-secondary);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .hwid-label {
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            color: var(--muted-foreground) !important;
            margin-bottom: 0.5rem;
        }

        .hwid-value {
            font-family: var(--font-mono) !important;
            font-size: 1rem !important;
            color: var(--foreground) !important;
            word-break: break-all;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background: var(--muted);
            border-radius: calc(var(--radius) - 2px);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .hwid-value:hover {
            background: var(--input);
            transform: translateY(-1px);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem !important;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--muted-foreground);
            animation: pulse 2s infinite;
        }

        .status-dot.validating {
            background: var(--primary);
        }

        .status-dot.success {
            background: var(--accent);
            animation: none;
        }

        .status-dot.error {
            background: var(--destructive);
            animation: none;
        }

        /* Support buttons */
        .support-section {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .support-btn {
            padding: 0.75rem 1.5rem;
            background: transparent !important;
            border: 1px solid var(--border) !important;
            border-radius: var(--radius) !important;
            color: var(--muted-foreground) !important;
            font-size: 0.875rem !important;
            cursor: pointer;
            transition: all 0.15s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .support-btn:hover {
            border-color: var(--primary);
            color: var(--primary);
            transform: scale(1.05);
        }

        /* Responsive design */
        @media (max-width: 640px) {
            .steam-container {
                padding: 1rem;
            }
            
            .steam-card {
                padding: 2rem;
            }
            
            .steam-title {
                font-size: 2rem !important;
            }
            
            .support-section {
                flex-direction: column;
            }

            .connection-status {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 2rem;
                align-self: stretch;
            }

            .server-metrics {
                grid-template-columns: 1fr;
            }
        }

        /* Toast notifications */
        .toast {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1rem 1.5rem;
            color: var(--foreground);
            box-shadow: 0 10px 15px 0 rgba(0, 0, 0, 0.5);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            border-color: var(--accent);
            background: linear-gradient(135deg, var(--accent) 0%, var(--card) 100%);
        }

        .toast.error {
            border-color: var(--destructive);
            background: linear-gradient(135deg, var(--destructive) 0%, var(--card) 100%);
        }
    </style>
</head>
<body>
    <div class="steam-container">
        <!-- Connection Status -->
        <div class="connection-status" id="connectionStatus">
            <div class="connection-indicator">
                <div class="connection-dot" id="connectionDot"></div>
                <div>
                    <span class="connection-text" id="connectionText">Connected to License Server</span>
                    <div class="connection-details" id="connectionDetails">auth.steamtools.com • 24ms</div>
                </div>
            </div>
        </div>

        <!-- Header -->
        <div class="steam-header">
            <h1 class="steam-title">🎮 STEAM TOOLS</h1>
            <p class="steam-subtitle">License Activation</p>
        </div>

        <!-- Main Card -->
        <div class="steam-card">
            <!-- Server Status Panel -->
            <div class="server-status-panel">
                <div class="server-status-header">
                    <div class="server-status-title">
                        <i data-lucide="server" class="server-icon"></i>
                        Authentication Server
                    </div>
                    <div class="connection-dot" id="serverStatusDot"></div>
                </div>
                <div class="server-metrics">
                    <div class="metric-item">
                        <div class="metric-label">Latency</div>
                        <div class="metric-value" id="latencyValue">24ms</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">Uptime</div>
                        <div class="metric-value" id="uptimeValue">99.9%</div>
                    </div>
                </div>
                <div class="data-flow-indicator">
                    <div class="data-flow-bar"></div>
                </div>
            </div>

            <!-- Logo Area -->
            <div class="steam-logo">
                <i data-lucide="gamepad-2" class="steam-logo-icon"></i>
                <div class="steam-logo-text">Steam Tools</div>
            </div>

            <!-- License Input -->
            <div class="license-section">
                <label class="license-label">License Key</label>
                <input 
                    type="text" 
                    class="license-input" 
                    id="licenseInput"
                    placeholder="XXXX-XXXX-XXXX-XXXX"
                    maxlength="19"
                >
            </div>

            <!-- Activate Button -->
            <button class="activate-btn" id="activateBtn">
                <div class="btn-content">
                    <span class="btn-text">Activate License</span>
                </div>
                <div class="spinner"></div>
            </button>

            <!-- Progress Bar -->
            <div class="progress-section">
                <div class="progress-label">
                    <span>Activation Progress</span>
                    <span id="progressText">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
            </div>

            <!-- HWID Section -->
            <div class="hwid-section">
                <div class="hwid-label">Hardware ID</div>
                <div class="hwid-value" id="hwidValue" onclick="copyHWID()">
                    ABC123-DEF456-GHI789-JKL012
                </div>
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">Ready</span>
                </div>
            </div>

            <!-- Support Buttons -->
            <div class="support-section">
                <a href="#" class="support-btn">
                    <i data-lucide="help-circle" style="width: 16px; height: 16px;"></i>
                    Help
                </a>
                <a href="#" class="support-btn">
                    <i data-lucide="message-circle" style="width: 16px; height: 16px;"></i>
                    Support
                </a>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer"></div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Connection status elements
        const connectionDot = document.getElementById('connectionDot');
        const connectionText = document.getElementById('connectionText');
        const connectionDetails = document.getElementById('connectionDetails');
        const serverStatusDot = document.getElementById('serverStatusDot');
        const latencyValue = document.getElementById('latencyValue');
        const uptimeValue = document.getElementById('uptimeValue');

        // License input elements
        const licenseInput = document.getElementById('licenseInput');
        const activateBtn = document.getElementById('activateBtn');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');

        // Simulate connection status updates
        function updateConnectionStatus() {
            const latencies = [18, 24, 31, 19, 27, 22, 29];
            const uptimes = ['99.9%', '99.8%', '100%', '99.7%'];
            
            setInterval(() => {
                const randomLatency = latencies[Math.floor(Math.random() * latencies.length)];
                const randomUptime = uptimes[Math.floor(Math.random() * uptimes.length)];
                
                latencyValue.textContent = randomLatency + 'ms';
                uptimeValue.textContent = randomUptime;
                connectionDetails.textContent = `auth.steamtools.com • ${randomLatency}ms`;
            }, 3000);
        }

        // Simulate connection establishment
        function simulateConnection() {
            // Start with connecting state
            connectionDot.classList.add('connecting');
            serverStatusDot.classList.add('connecting');
            connectionText.textContent = 'Connecting to License Server';
            connectionDetails.textContent = 'Establishing secure connection...';
            
            setTimeout(() => {
                // Connected state
                connectionDot.classList.remove('connecting');
                serverStatusDot.classList.remove('connecting');
                connectionText.textContent = 'Connected to License Server';
                connectionDetails.textContent = 'auth.steamtools.com • 24ms';
                
                showToast('Successfully connected to license server', 'success');
                updateConnectionStatus();
            }, 2000);
        }

        // Format license key input
        licenseInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
            let formatted = value.match(/.{1,4}/g)?.join('-') || value;
            if (formatted.length > 19) formatted = formatted.substring(0, 19);
            e.target.value = formatted;
            
            // Validate format
            const isValid = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/.test(formatted);
            e.target.classList.toggle('success', isValid);
            e.target.classList.toggle('error', formatted.length === 19 && !isValid);
            
            activateBtn.disabled = !isValid;
        });

        // Activation process
        activateBtn.addEventListener('click', async function() {
            if (this.disabled) return;
            
            this.disabled = true;
            this.classList.add('loading');
            
            updateStatus('validating', 'Validating license...');
            
            // Simulate activation process
            await simulateActivation();
        });

        async function simulateActivation() {
            const steps = [
                { progress: 20, status: 'Connecting to authentication server...' },
                { progress: 40, status: 'Verifying license key...' },
                { progress: 60, status: 'Checking hardware ID...' },
                { progress: 80, status: 'Activating license...' },
                { progress: 100, status: 'License activated successfully!' }
            ];
            
            for (let i = 0; i < steps.length; i++) {
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                const step = steps[i];
                updateProgress(step.progress);
                updateStatus(i === steps.length - 1 ? 'success' : 'validating', step.status);
                
                if (i === steps.length - 1) {
                    activateBtn.classList.remove('loading');
                    activateBtn.innerHTML = `
                        <div class="btn-content">
                            <i data-lucide="check" class="btn-icon"></i>
                            <span class="btn-text">Activated</span>
                        </div>
                    `;
                    activateBtn.style.background = 'var(--steam-gradient-success)';
                    showToast('License activated successfully!', 'success');
                    lucide.createIcons();
                }
            }
        }

        function updateProgress(percent) {
            progressFill.style.width = percent + '%';
            progressText.textContent = percent + '%';
        }

        function updateStatus(type, message) {
            statusDot.className = 'status-dot ' + type;
            statusText.textContent = message;
        }

        function copyHWID() {
            const hwidValue = document.getElementById('hwidValue').textContent;
            navigator.clipboard.writeText(hwidValue).then(() => {
                showToast('Hardware ID copied to clipboard!', 'success');
            });
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <i data-lucide="${type === 'success' ? 'check-circle' : 'info'}" style="width: 16px; height: 16px;"></i>
                    ${message}
                </div>
            `;
            
            document.getElementById('toastContainer').appendChild(toast);
            lucide.createIcons();
            
            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // Initialize
        updateStatus('ready', 'Ready');
        simulateConnection();
    </script>
</body>
</html>